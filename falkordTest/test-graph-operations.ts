/**
 * Test script for FalkorDB graph operations
 * This script tests the graph creation, node operations, and data import functionality
 */

const BASE_URL = 'http://localhost:3000';

interface TestResult {
  test: string;
  success: boolean;
  message: string;
  data?: any;
  error?: string;
}

class FalkorDBTester {
  private results: TestResult[] = [];
  private testGraphName = 'test_knowledge_graph';

  async runAllTests(): Promise<void> {
    console.log('🚀 Starting FalkorDB API Tests...\n');

    try {
      // Test 1: Health Check
      await this.testHealthCheck();

      // Test 2: Create Graph
      await this.testCreateGraph();

      // Test 3: Check Graph Exists
      await this.testGraphExists();

      // Test 4: Create Single Category Node
      await this.testCreateSingleCategoryNode();

      // Test 5: Create Single Record Node
      await this.testCreateSingleRecordNode();

      // Test 6: Upsert Category Node
      await this.testUpsertCategoryNode();

      // Test 7: Upsert Record Node
      await this.testUpsertRecordNode();

      // Test 8: Bulk Import Sample Data
      await this.testBulkImport();

      // Test 9: Get Graph Statistics
      await this.testGetGraphStats();

      // Test 10: Delete Graph (cleanup)
      await this.testDeleteGraph();

    } catch (error) {
      console.error('❌ Test suite failed:', error);
    }

    this.printResults();
  }

  private async testHealthCheck(): Promise<void> {
    try {
      const response = await fetch(`${BASE_URL}/api/falkor/graph?action=health`);
      const data = await response.json();

      this.results.push({
        test: 'Health Check',
        success: response.ok && data.status === 'healthy',
        message: data.message || 'Health check completed',
        data
      });
    } catch (error) {
      this.results.push({
        test: 'Health Check',
        success: false,
        message: 'Failed to perform health check',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  private async testCreateGraph(): Promise<void> {
    try {
      const response = await fetch(`${BASE_URL}/api/falkor/graph`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          graphName: this.testGraphName,
          overwrite: true
        })
      });

      const data = await response.json();

      this.results.push({
        test: 'Create Graph',
        success: response.ok && data.success,
        message: data.message || 'Graph creation completed',
        data
      });
    } catch (error) {
      this.results.push({
        test: 'Create Graph',
        success: false,
        message: 'Failed to create graph',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  private async testGraphExists(): Promise<void> {
    try {
      const response = await fetch(
        `${BASE_URL}/api/falkor/graph?action=exists&name=${this.testGraphName}`
      );
      const data = await response.json();

      this.results.push({
        test: 'Check Graph Exists',
        success: response.ok && data.exists === true,
        message: `Graph exists: ${data.exists}`,
        data
      });
    } catch (error) {
      this.results.push({
        test: 'Check Graph Exists',
        success: false,
        message: 'Failed to check graph existence',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  private async testCreateSingleCategoryNode(): Promise<void> {
    try {
      const categoryNode = {
        id: 'test-cat-1',
        name: 'Test Category',
        country: 'global',
        summary: 'This is a test category',
        refinedSummary: 'Refined test category summary',
        nodeType: 'category',
        level: 0,
        path: 'Test Category'
      };

      const response = await fetch(`${BASE_URL}/api/falkor/nodes`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          graphName: this.testGraphName,
          operation: 'create',
          data: categoryNode
        })
      });

      const data = await response.json();

      this.results.push({
        test: 'Create Single Category Node',
        success: response.ok && data.success,
        message: data.message || 'Category node creation completed',
        data
      });
    } catch (error) {
      this.results.push({
        test: 'Create Single Category Node',
        success: false,
        message: 'Failed to create category node',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  private async testCreateSingleRecordNode(): Promise<void> {
    try {
      const recordNode = {
        id: 'test-rec-1',
        recordId: 999999,
        title: 'Test Record',
        htmlUrl: 'https://test.example.com',
        breadcrumb: 'Test Category > Test Record',
        isDeepest: true,
        country: 'global',
        sectionId: 888888,
        cleanedBody: 'This is a test record with sample content for testing purposes.',
        nodeType: 'record'
      };

      const response = await fetch(`${BASE_URL}/api/falkor/nodes`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          graphName: this.testGraphName,
          operation: 'create',
          data: recordNode
        })
      });

      const data = await response.json();

      this.results.push({
        test: 'Create Single Record Node',
        success: response.ok && data.success,
        message: data.message || 'Record node creation completed',
        data
      });
    } catch (error) {
      this.results.push({
        test: 'Create Single Record Node',
        success: false,
        message: 'Failed to create record node',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  private async testUpsertCategoryNode(): Promise<void> {
    try {
      const categoryNode = {
        id: 'test-cat-1', // Same ID as before
        name: 'Updated Test Category',
        country: 'global',
        summary: 'This is an updated test category',
        refinedSummary: 'Updated refined test category summary',
        nodeType: 'category',
        level: 0,
        path: 'Updated Test Category'
      };

      const response = await fetch(`${BASE_URL}/api/falkor/nodes`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          graphName: this.testGraphName,
          operation: 'upsert',
          data: categoryNode
        })
      });

      const data = await response.json();

      this.results.push({
        test: 'Upsert Category Node',
        success: response.ok && data.success,
        message: data.message || 'Category node upsert completed',
        data
      });
    } catch (error) {
      this.results.push({
        test: 'Upsert Category Node',
        success: false,
        message: 'Failed to upsert category node',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  private async testUpsertRecordNode(): Promise<void> {
    try {
      const recordNode = {
        id: 'test-rec-1', // Same ID as before
        recordId: 999999,
        title: 'Updated Test Record',
        htmlUrl: 'https://updated-test.example.com',
        breadcrumb: 'Updated Test Category > Updated Test Record',
        isDeepest: true,
        country: 'global',
        sectionId: 888888,
        cleanedBody: 'This is an updated test record with modified content for testing purposes.',
        nodeType: 'record'
      };

      const response = await fetch(`${BASE_URL}/api/falkor/nodes`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          graphName: this.testGraphName,
          operation: 'upsert',
          data: recordNode
        })
      });

      const data = await response.json();

      this.results.push({
        test: 'Upsert Record Node',
        success: response.ok && data.success,
        message: data.message || 'Record node upsert completed',
        data
      });
    } catch (error) {
      this.results.push({
        test: 'Upsert Record Node',
        success: false,
        message: 'Failed to upsert record node',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  private async testBulkImport(): Promise<void> {
    try {
      const sampleData = {
        children: {
          "Test Documentation": {
            id: "test-doc-1",
            country: "global",
            summary: "Test documentation category",
            refinedSummary: "Comprehensive test documentation for API testing",
            children: {
              "API Testing": {
                id: "test-api-1",
                country: "global",
                summary: "API testing subcategory",
                refinedSummary: "Guidelines and examples for API testing",
                children: {},
                records: [
                  {
                    id: "test-api-rec-1",
                    fields: {
                      ID: 777777,
                      "HTML URL": "https://test-docs.example.com/api-testing",
                      Title: "API Testing Best Practices",
                      Breadcrumb: "Test Documentation > API Testing",
                      "Is Deepest": "true",
                      Country: "global",
                      "Section ID": 666666,
                      "Cleaned Body": "This document covers best practices for API testing including unit tests, integration tests, and end-to-end testing strategies."
                    }
                  }
                ]
              }
            },
            records: []
          }
        }
      };

      const response = await fetch(`${BASE_URL}/api/falkor/nodes`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          graphName: this.testGraphName,
          operation: 'create',
          nodeType: 'all',
          data: sampleData
        })
      });

      const data = await response.json();

      this.results.push({
        test: 'Bulk Import Sample Data',
        success: response.ok && data.success,
        message: data.message || 'Bulk import completed',
        data
      });
    } catch (error) {
      this.results.push({
        test: 'Bulk Import Sample Data',
        success: false,
        message: 'Failed to perform bulk import',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  private async testGetGraphStats(): Promise<void> {
    try {
      const response = await fetch(
        `${BASE_URL}/api/falkor/graph?action=stats&name=${this.testGraphName}`
      );
      const data = await response.json();

      this.results.push({
        test: 'Get Graph Statistics',
        success: response.ok && data.success,
        message: `Graph stats retrieved: ${JSON.stringify(data.stats)}`,
        data
      });
    } catch (error) {
      this.results.push({
        test: 'Get Graph Statistics',
        success: false,
        message: 'Failed to get graph statistics',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  private async testDeleteGraph(): Promise<void> {
    try {
      const response = await fetch(
        `${BASE_URL}/api/falkor/graph?name=${this.testGraphName}`,
        { method: 'DELETE' }
      );
      const data = await response.json();

      this.results.push({
        test: 'Delete Graph (Cleanup)',
        success: response.ok && data.success,
        message: data.message || 'Graph deletion completed',
        data
      });
    } catch (error) {
      this.results.push({
        test: 'Delete Graph (Cleanup)',
        success: false,
        message: 'Failed to delete graph',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  private printResults(): void {
    console.log('\n📊 Test Results Summary:');
    console.log('========================\n');

    const passed = this.results.filter(r => r.success).length;
    const total = this.results.length;

    this.results.forEach((result, index) => {
      const status = result.success ? '✅' : '❌';
      console.log(`${index + 1}. ${status} ${result.test}`);
      console.log(`   ${result.message}`);
      if (result.error) {
        console.log(`   Error: ${result.error}`);
      }
      console.log('');
    });

    console.log(`\n🎯 Results: ${passed}/${total} tests passed`);
    
    if (passed === total) {
      console.log('🎉 All tests passed! FalkorDB integration is working correctly.');
    } else {
      console.log('⚠️  Some tests failed. Please check the errors above.');
    }
  }
}

// Export for use in other scripts
export { FalkorDBTester };

// Run tests if this file is executed directly
if (require.main === module) {
  const tester = new FalkorDBTester();
  tester.runAllTests().catch(console.error);
}
