/**
 * Simple Node.js test runner for FalkorDB API endpoints
 * Run with: node falkordTest/run-tests.js
 */

const BASE_URL = 'http://localhost:3000';

class FalkorDBTester {
  constructor() {
    this.results = [];
    this.testGraphName = 'test_knowledge_graph';
  }

  async runAllTests() {
    console.log('🚀 Starting FalkorDB API Tests...\n');

    try {
      // Test 1: Health Check
      await this.testHealthCheck();

      // Test 2: Create Graph
      await this.testCreateGraph();

      // Test 3: Check Graph Exists
      await this.testGraphExists();

      // Test 4: Create Single Category Node
      await this.testCreateSingleCategoryNode();

      // Test 5: Create Single Record Node
      await this.testCreateSingleRecordNode();

      // Test 6: Upsert Category Node
      await this.testUpsertCategoryNode();

      // Test 7: Bulk Import Sample Data
      await this.testBulkImport();

      // Test 8: Get Graph Statistics
      await this.testGetGraphStats();

      // Test 9: Delete Graph (cleanup)
      await this.testDeleteGraph();

    } catch (error) {
      console.error('❌ Test suite failed:', error);
    }

    this.printResults();
  }

  async makeRequest(url, options = {}) {
    try {
      const fetch = (await import('node-fetch')).default;
      const response = await fetch(url, options);
      const data = await response.json();
      return { response, data };
    } catch (error) {
      // Fallback for environments without node-fetch
      console.log('Note: Install node-fetch for better testing: npm install node-fetch');
      return { response: { ok: false }, data: { error: 'Fetch not available' } };
    }
  }

  async testHealthCheck() {
    try {
      const { response, data } = await this.makeRequest(`${BASE_URL}/api/falkor/graph?action=health`);

      this.results.push({
        test: 'Health Check',
        success: response.ok && data.status === 'healthy',
        message: data.message || 'Health check completed',
        data
      });
    } catch (error) {
      this.results.push({
        test: 'Health Check',
        success: false,
        message: 'Failed to perform health check',
        error: error.message
      });
    }
  }

  async testCreateGraph() {
    try {
      const { response, data } = await this.makeRequest(`${BASE_URL}/api/falkor/graph`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          graphName: this.testGraphName,
          overwrite: true
        })
      });

      this.results.push({
        test: 'Create Graph',
        success: response.ok && data.success,
        message: data.message || 'Graph creation completed',
        data
      });
    } catch (error) {
      this.results.push({
        test: 'Create Graph',
        success: false,
        message: 'Failed to create graph',
        error: error.message
      });
    }
  }

  async testGraphExists() {
    try {
      const { response, data } = await this.makeRequest(
        `${BASE_URL}/api/falkor/graph?action=exists&name=${this.testGraphName}`
      );

      this.results.push({
        test: 'Check Graph Exists',
        success: response.ok && data.exists === true,
        message: `Graph exists: ${data.exists}`,
        data
      });
    } catch (error) {
      this.results.push({
        test: 'Check Graph Exists',
        success: false,
        message: 'Failed to check graph existence',
        error: error.message
      });
    }
  }

  async testCreateSingleCategoryNode() {
    try {
      const categoryNode = {
        id: 'test-cat-1',
        name: 'Test Category',
        country: 'global',
        summary: 'This is a test category',
        refinedSummary: 'Refined test category summary',
        nodeType: 'category',
        level: 0,
        path: 'Test Category'
      };

      const { response, data } = await this.makeRequest(`${BASE_URL}/api/falkor/nodes`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          graphName: this.testGraphName,
          operation: 'create',
          data: categoryNode
        })
      });

      this.results.push({
        test: 'Create Single Category Node',
        success: response.ok && data.success,
        message: data.message || 'Category node creation completed',
        data
      });
    } catch (error) {
      this.results.push({
        test: 'Create Single Category Node',
        success: false,
        message: 'Failed to create category node',
        error: error.message
      });
    }
  }

  async testCreateSingleRecordNode() {
    try {
      const recordNode = {
        id: 'test-rec-1',
        recordId: 999999,
        title: 'Test Record',
        htmlUrl: 'https://test.example.com',
        breadcrumb: 'Test Category > Test Record',
        isDeepest: true,
        country: 'global',
        sectionId: 888888,
        cleanedBody: 'This is a test record with sample content for testing purposes.',
        nodeType: 'record'
      };

      const { response, data } = await this.makeRequest(`${BASE_URL}/api/falkor/nodes`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          graphName: this.testGraphName,
          operation: 'create',
          data: recordNode
        })
      });

      this.results.push({
        test: 'Create Single Record Node',
        success: response.ok && data.success,
        message: data.message || 'Record node creation completed',
        data
      });
    } catch (error) {
      this.results.push({
        test: 'Create Single Record Node',
        success: false,
        message: 'Failed to create record node',
        error: error.message
      });
    }
  }

  async testUpsertCategoryNode() {
    try {
      const categoryNode = {
        id: 'test-cat-1', // Same ID as before
        name: 'Updated Test Category',
        country: 'global',
        summary: 'This is an updated test category',
        refinedSummary: 'Updated refined test category summary',
        nodeType: 'category',
        level: 0,
        path: 'Updated Test Category'
      };

      const { response, data } = await this.makeRequest(`${BASE_URL}/api/falkor/nodes`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          graphName: this.testGraphName,
          operation: 'upsert',
          data: categoryNode
        })
      });

      this.results.push({
        test: 'Upsert Category Node',
        success: response.ok && data.success,
        message: data.message || 'Category node upsert completed',
        data
      });
    } catch (error) {
      this.results.push({
        test: 'Upsert Category Node',
        success: false,
        message: 'Failed to upsert category node',
        error: error.message
      });
    }
  }

  async testBulkImport() {
    try {
      const sampleData = {
        children: {
          "Test Documentation": {
            id: "test-doc-1",
            country: "global",
            summary: "Test documentation category",
            refinedSummary: "Comprehensive test documentation for API testing",
            children: {
              "API Testing": {
                id: "test-api-1",
                country: "global",
                summary: "API testing subcategory",
                refinedSummary: "Guidelines and examples for API testing",
                children: {},
                records: [
                  {
                    id: "test-api-rec-1",
                    fields: {
                      ID: 777777,
                      "HTML URL": "https://test-docs.example.com/api-testing",
                      Title: "API Testing Best Practices",
                      Breadcrumb: "Test Documentation > API Testing",
                      "Is Deepest": "true",
                      Country: "global",
                      "Section ID": 666666,
                      "Cleaned Body": "This document covers best practices for API testing including unit tests, integration tests, and end-to-end testing strategies."
                    }
                  }
                ]
              }
            },
            records: []
          }
        }
      };

      const { response, data } = await this.makeRequest(`${BASE_URL}/api/falkor/nodes`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          graphName: this.testGraphName,
          operation: 'create',
          nodeType: 'all',
          data: sampleData
        })
      });

      this.results.push({
        test: 'Bulk Import Sample Data',
        success: response.ok && data.success,
        message: data.message || 'Bulk import completed',
        data
      });
    } catch (error) {
      this.results.push({
        test: 'Bulk Import Sample Data',
        success: false,
        message: 'Failed to perform bulk import',
        error: error.message
      });
    }
  }

  async testGetGraphStats() {
    try {
      const { response, data } = await this.makeRequest(
        `${BASE_URL}/api/falkor/graph?action=stats&name=${this.testGraphName}`
      );

      this.results.push({
        test: 'Get Graph Statistics',
        success: response.ok && data.success,
        message: `Graph stats retrieved: ${JSON.stringify(data.stats)}`,
        data
      });
    } catch (error) {
      this.results.push({
        test: 'Get Graph Statistics',
        success: false,
        message: 'Failed to get graph statistics',
        error: error.message
      });
    }
  }

  async testDeleteGraph() {
    try {
      const { response, data } = await this.makeRequest(
        `${BASE_URL}/api/falkor/graph?name=${this.testGraphName}`,
        { method: 'DELETE' }
      );

      this.results.push({
        test: 'Delete Graph (Cleanup)',
        success: response.ok && data.success,
        message: data.message || 'Graph deletion completed',
        data
      });
    } catch (error) {
      this.results.push({
        test: 'Delete Graph (Cleanup)',
        success: false,
        message: 'Failed to delete graph',
        error: error.message
      });
    }
  }

  printResults() {
    console.log('\n📊 Test Results Summary:');
    console.log('========================\n');

    const passed = this.results.filter(r => r.success).length;
    const total = this.results.length;

    this.results.forEach((result, index) => {
      const status = result.success ? '✅' : '❌';
      console.log(`${index + 1}. ${status} ${result.test}`);
      console.log(`   ${result.message}`);
      if (result.error) {
        console.log(`   Error: ${result.error}`);
      }
      console.log('');
    });

    console.log(`\n🎯 Results: ${passed}/${total} tests passed`);
    
    if (passed === total) {
      console.log('🎉 All tests passed! FalkorDB integration is working correctly.');
    } else {
      console.log('⚠️  Some tests failed. Please check the errors above.');
    }
  }
}

// Run tests
const tester = new FalkorDBTester();
tester.runAllTests().catch(console.error);
