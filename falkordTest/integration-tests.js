/**
 * Comprehensive integration test suite for FalkorDB operations
 * Tests data integrity, relationships, and complex operations
 * Run with: node falkordTest/integration-tests.js
 */

const BASE_URL = 'http://localhost:3000';

class IntegrationTestSuite {
  constructor() {
    this.results = [];
    this.testGraphName = 'integration_test_graph';
  }

  async makeRequest(url, options = {}) {
    try {
      const fetch = (await import('node-fetch')).default;
      const response = await fetch(url, options);
      const data = await response.json();
      return { response, data };
    } catch (error) {
      console.log('Note: Install node-fetch for better testing: npm install node-fetch');
      return { response: { ok: false }, data: { error: 'Fetch not available' } };
    }
  }

  async runIntegrationTests() {
    console.log('🧪 Starting Integration Test Suite...\n');

    try {
      // Setup
      await this.setupTestEnvironment();

      // Core functionality tests
      await this.testGraphLifecycle();
      await this.testNodeCreationAndRetrieval();
      await this.testNodeUpsertBehavior();
      await this.testBulkOperations();
      await this.testDataIntegrity();
      await this.testRelationshipCreation();
      await this.testErrorHandling();
      await this.testConcurrentOperations();

      // Cleanup
      await this.cleanupTestEnvironment();

    } catch (error) {
      console.error('❌ Integration test suite failed:', error);
    }

    this.printResults();
  }

  async setupTestEnvironment() {
    console.log('🔧 Setting up test environment...');
    
    // Clean up any existing test graph
    await this.makeRequest(`${BASE_URL}/api/falkor/graph?name=${this.testGraphName}`, {
      method: 'DELETE'
    });

    // Create fresh test graph
    const { response, data } = await this.makeRequest(`${BASE_URL}/api/falkor/graph`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        graphName: this.testGraphName,
        overwrite: true
      })
    });

    this.addResult('Setup Test Environment', response.ok && data.success, 
      data.message || 'Environment setup completed');
  }

  async testGraphLifecycle() {
    console.log('🔄 Testing graph lifecycle...');

    // Test graph creation with invalid name
    const { response: invalidResponse } = await this.makeRequest(`${BASE_URL}/api/falkor/graph`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        graphName: 'invalid-graph-name!',
        overwrite: false
      })
    });

    this.addResult('Graph Creation - Invalid Name Validation', 
      !invalidResponse.ok, 'Invalid graph name properly rejected');

    // Test graph existence check
    const { response: existsResponse, data: existsData } = await this.makeRequest(
      `${BASE_URL}/api/falkor/graph?action=exists&name=${this.testGraphName}`
    );

    this.addResult('Graph Existence Check', 
      existsResponse.ok && existsData.exists === true, 
      'Graph existence properly detected');
  }

  async testNodeCreationAndRetrieval() {
    console.log('📝 Testing node creation and retrieval...');

    // Create a category node
    const categoryNode = {
      id: 'test-category-1',
      name: 'Integration Test Category',
      country: 'global',
      summary: 'Category for integration testing',
      refinedSummary: 'Detailed category for comprehensive testing',
      nodeType: 'category',
      level: 0,
      path: 'Integration Test Category'
    };

    const { response: catResponse, data: catData } = await this.makeRequest(`${BASE_URL}/api/falkor/nodes`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        graphName: this.testGraphName,
        operation: 'create',
        data: categoryNode
      })
    });

    this.addResult('Category Node Creation', 
      catResponse.ok && catData.success, 
      catData.message || 'Category node created');

    // Create a record node
    const recordNode = {
      id: 'test-record-1',
      recordId: 123456,
      title: 'Integration Test Record',
      htmlUrl: 'https://integration-test.example.com',
      breadcrumb: 'Integration Test Category > Integration Test Record',
      isDeepest: true,
      country: 'global',
      sectionId: 789012,
      cleanedBody: 'This is a comprehensive test record for integration testing purposes.',
      nodeType: 'record'
    };

    const { response: recResponse, data: recData } = await this.makeRequest(`${BASE_URL}/api/falkor/nodes`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        graphName: this.testGraphName,
        operation: 'create',
        data: recordNode
      })
    });

    this.addResult('Record Node Creation', 
      recResponse.ok && recData.success, 
      recData.message || 'Record node created');
  }

  async testNodeUpsertBehavior() {
    console.log('🔄 Testing node upsert behavior...');

    // Upsert existing category node with changes
    const updatedCategoryNode = {
      id: 'test-category-1', // Same ID
      name: 'Updated Integration Test Category',
      country: 'global',
      summary: 'Updated category for integration testing',
      refinedSummary: 'Updated detailed category for comprehensive testing',
      nodeType: 'category',
      level: 0,
      path: 'Updated Integration Test Category'
    };

    const { response: upsertResponse, data: upsertData } = await this.makeRequest(`${BASE_URL}/api/falkor/nodes`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        graphName: this.testGraphName,
        operation: 'upsert',
        data: updatedCategoryNode
      })
    });

    this.addResult('Node Upsert Operation', 
      upsertResponse.ok && upsertData.success, 
      upsertData.message || 'Node upsert completed');

    // Upsert new node (should create)
    const newCategoryNode = {
      id: 'test-category-2',
      name: 'New Category via Upsert',
      country: 'global',
      summary: 'New category created via upsert',
      refinedSummary: 'This category was created using upsert operation',
      nodeType: 'category',
      level: 1,
      path: 'New Category via Upsert'
    };

    const { response: newUpsertResponse, data: newUpsertData } = await this.makeRequest(`${BASE_URL}/api/falkor/nodes`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        graphName: this.testGraphName,
        operation: 'upsert',
        data: newCategoryNode
      })
    });

    this.addResult('Upsert New Node Creation', 
      newUpsertResponse.ok && newUpsertData.success, 
      newUpsertData.message || 'New node created via upsert');
  }

  async testBulkOperations() {
    console.log('📦 Testing bulk operations...');

    const bulkData = {
      children: {
        "Bulk Test Category": {
          id: "bulk-cat-1",
          country: "global",
          summary: "Bulk test category",
          refinedSummary: "Category created via bulk operation",
          children: {
            "Bulk Subcategory": {
              id: "bulk-subcat-1",
              country: "global",
              summary: "Bulk test subcategory",
              refinedSummary: "Subcategory created via bulk operation",
              children: {},
              records: [
                {
                  id: "bulk-rec-1",
                  fields: {
                    ID: 555555,
                    "HTML URL": "https://bulk-test.example.com",
                    Title: "Bulk Test Record",
                    Breadcrumb: "Bulk Test Category > Bulk Subcategory > Bulk Test Record",
                    "Is Deepest": "true",
                    Country: "global",
                    "Section ID": 444444,
                    "Cleaned Body": "This record was created via bulk import operation."
                  }
                }
              ]
            }
          },
          records: []
        }
      }
    };

    const { response: bulkResponse, data: bulkData_result } = await this.makeRequest(`${BASE_URL}/api/falkor/nodes`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        graphName: this.testGraphName,
        operation: 'create',
        nodeType: 'all',
        data: bulkData
      })
    });

    this.addResult('Bulk Data Import', 
      bulkResponse.ok && bulkData_result.success, 
      `Bulk import: ${JSON.stringify(bulkData_result.summary)}`);
  }

  async testDataIntegrity() {
    console.log('🔍 Testing data integrity...');

    // Get graph statistics to verify data integrity
    const { response: statsResponse, data: statsData } = await this.makeRequest(
      `${BASE_URL}/api/falkor/graph?action=stats&name=${this.testGraphName}`
    );

    const hasNodes = statsData.stats && statsData.stats.nodeCount > 0;
    this.addResult('Data Integrity Check', 
      statsResponse.ok && hasNodes, 
      `Graph contains ${statsData.stats?.nodeCount || 0} nodes and ${statsData.stats?.edgeCount || 0} edges`);
  }

  async testRelationshipCreation() {
    console.log('🔗 Testing relationship creation...');

    // Relationships should be created automatically during bulk import
    // This test verifies that the relationship creation logic works
    const { response: statsResponse, data: statsData } = await this.makeRequest(
      `${BASE_URL}/api/falkor/graph?action=stats&name=${this.testGraphName}`
    );

    const hasRelationships = statsData.stats && statsData.stats.edgeCount > 0;
    this.addResult('Relationship Creation', 
      statsResponse.ok && hasRelationships, 
      `Graph contains ${statsData.stats?.edgeCount || 0} relationships`);
  }

  async testErrorHandling() {
    console.log('⚠️ Testing error handling...');

    // Test creating node in non-existent graph
    const { response: errorResponse } = await this.makeRequest(`${BASE_URL}/api/falkor/nodes`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        graphName: 'non_existent_graph',
        operation: 'create',
        data: { id: 'test', nodeType: 'category' }
      })
    });

    this.addResult('Error Handling - Non-existent Graph', 
      !errorResponse.ok, 
      'Properly rejected operation on non-existent graph');

    // Test invalid node data
    const { response: invalidDataResponse } = await this.makeRequest(`${BASE_URL}/api/falkor/nodes`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        graphName: this.testGraphName,
        operation: 'create',
        data: { invalid: 'data' }
      })
    });

    this.addResult('Error Handling - Invalid Data', 
      !invalidDataResponse.ok, 
      'Properly rejected invalid node data');
  }

  async testConcurrentOperations() {
    console.log('⚡ Testing concurrent operations...');

    // Create multiple nodes concurrently
    const concurrentPromises = [];
    for (let i = 0; i < 3; i++) {
      const promise = this.makeRequest(`${BASE_URL}/api/falkor/nodes`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          graphName: this.testGraphName,
          operation: 'create',
          data: {
            id: `concurrent-node-${i}`,
            name: `Concurrent Node ${i}`,
            country: 'global',
            summary: `Concurrent test node ${i}`,
            refinedSummary: `Node created in concurrent test ${i}`,
            nodeType: 'category',
            level: 0,
            path: `Concurrent Node ${i}`
          }
        })
      });
      concurrentPromises.push(promise);
    }

    const results = await Promise.all(concurrentPromises);
    const allSuccessful = results.every(({ response, data }) => response.ok && data.success);

    this.addResult('Concurrent Operations', 
      allSuccessful, 
      `${results.length} concurrent operations completed`);
  }

  async cleanupTestEnvironment() {
    console.log('🧹 Cleaning up test environment...');

    const { response, data } = await this.makeRequest(
      `${BASE_URL}/api/falkor/graph?name=${this.testGraphName}`,
      { method: 'DELETE' }
    );

    this.addResult('Cleanup Test Environment', 
      response.ok && data.success, 
      data.message || 'Environment cleanup completed');
  }

  addResult(test, success, message, data = null) {
    this.results.push({ test, success, message, data });
  }

  printResults() {
    console.log('\n🧪 Integration Test Results:');
    console.log('=============================\n');

    const passed = this.results.filter(r => r.success).length;
    const total = this.results.length;

    this.results.forEach((result, index) => {
      const status = result.success ? '✅' : '❌';
      console.log(`${index + 1}. ${status} ${result.test}`);
      console.log(`   ${result.message}`);
      console.log('');
    });

    console.log(`\n🎯 Integration Test Results: ${passed}/${total} tests passed`);
    
    if (passed === total) {
      console.log('🎉 All integration tests passed! FalkorDB integration is robust and reliable.');
    } else {
      console.log('⚠️  Some integration tests failed. Please review the implementation.');
    }

    // Performance summary
    console.log('\n📊 Test Coverage Summary:');
    console.log('- Graph lifecycle management ✓');
    console.log('- Node creation and retrieval ✓');
    console.log('- Upsert operations ✓');
    console.log('- Bulk data operations ✓');
    console.log('- Data integrity verification ✓');
    console.log('- Relationship creation ✓');
    console.log('- Error handling ✓');
    console.log('- Concurrent operations ✓');
  }
}

// Run integration tests
if (require.main === module) {
  const testSuite = new IntegrationTestSuite();
  testSuite.runIntegrationTests().catch(console.error);
}

module.exports = { IntegrationTestSuite };
