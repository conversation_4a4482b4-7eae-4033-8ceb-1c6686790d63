export interface Node {
  id: string
  path: string
  country: string
  summary: string
  refined_summary: string
  record_count: number
  has_children: boolean
  children: Node[]
  has_records: boolean
}

export interface Record {
  id: string
  fields: {
    ID: number
    "HTML URL": string
    Title: string
    Breadcrumb: string
    "Is Deepest": string
    Country: string
    "Section ID": number
    "Cleaned Body": string
  }
  _bm42_score: number
  _search_method: "bm42" | "text-search" | "urls"
  _node_path: string
}

export interface SearchFilters {
  country?: string
  path?: string
  recordType?: string
}

export interface SearchState {
  query: string
  mode: "nodes" | "records" | "urls"
  filters: SearchFilters
  results: (Node | Record)[]
  isLoading: boolean
  error?: string
}
