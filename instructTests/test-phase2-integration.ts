import { getEvaluationService } from '../lib/evaluation-utils';

async function testPhase2Integration() {
  console.log('🚀 Testing Phase 2: API Integration\n');
  
  // Test 1: Health Check Endpoint
  console.log('📊 Test 1: Health Check Endpoint');
  try {
    const healthResponse = await fetch('http://localhost:3000/api/evaluation/health');
    const healthData = await healthResponse.json();
    
    console.log(`   Status: ${healthResponse.status}`);
    console.log(`   Service Status: ${healthData.status}`);
    console.log(`   Response Time: ${healthData.responseTime}ms`);
    console.log(`   Gemini API: ${healthData.services?.geminiApi}`);
    console.log('   ✅ Health check passed\n');
  } catch (error) {
    console.log(`   ❌ Health check failed: ${error}\n`);
  }

  // Test 2: Metrics Endpoint
  console.log('📈 Test 2: Metrics Endpoint');
  try {
    const metricsResponse = await fetch('http://localhost:3000/api/evaluation/metrics');
    const metricsData = await metricsResponse.json();
    
    console.log(`   Total Evaluations: ${metricsData.totalEvaluations}`);
    console.log(`   Success Rate: ${metricsData.successRate}`);
    console.log(`   Average Response Time: ${metricsData.averageResponseTime?.toFixed(2)}ms`);
    console.log(`   Score Distribution:`);
    console.log(`     Excellent (90-100): ${metricsData.scoreDistribution?.excellent || 0}`);
    console.log(`     Good (70-89): ${metricsData.scoreDistribution?.good || 0}`);
    console.log(`     Fair (40-69): ${metricsData.scoreDistribution?.fair || 0}`);
    console.log(`     Poor (1-39): ${metricsData.scoreDistribution?.poor || 0}`);
    console.log(`     Unsupported (0): ${metricsData.scoreDistribution?.unsupported || 0}`);
    console.log('   ✅ Metrics endpoint working\n');
  } catch (error) {
    console.log(`   ❌ Metrics endpoint failed: ${error}\n`);
  }

  // Test 3: Single Evaluation API
  console.log('🔍 Test 3: Single Evaluation API');
  try {
    const evaluationData = {
      question: "What are the benefits of renewable energy?",
      answer: "Renewable energy sources like solar and wind power provide clean electricity without harmful emissions. They help reduce our dependence on fossil fuels and contribute to fighting climate change.",
      context: "Solar energy provides clean electricity without emissions. Wind power is another renewable source that generates electricity from natural wind currents. Both solar and wind energy help reduce greenhouse gas emissions and combat climate change. Renewable energy sources are becoming more cost-effective and reliable."
    };

    const evalResponse = await fetch('http://localhost:3000/api/evaluation', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(evaluationData)
    });

    const evalResult = await evalResponse.json();
    
    console.log(`   Status: ${evalResponse.status}`);
    console.log(`   Overall Score: ${evalResult.evaluation?.overall_score}/100`);
    console.log(`   Claims Analyzed: ${evalResult.evaluation?.evaluation_metadata?.total_claims}`);
    console.log(`   Supported Claims: ${evalResult.evaluation?.evaluation_metadata?.supported_claims}`);
    console.log(`   Duration: ${evalResult.duration}ms`);
    console.log('   ✅ Single evaluation API working\n');
  } catch (error) {
    console.log(`   ❌ Single evaluation API failed: ${error}\n`);
  }

  // Test 4: Batch Evaluation API
  console.log('📦 Test 4: Batch Evaluation API');
  try {
    const batchData = {
      batch: true,
      evaluations: [
        {
          index: 0,
          question: "What is solar energy?",
          answer: "Solar energy is a renewable source that converts sunlight into electricity.",
          context: "Solar energy harnesses sunlight to generate clean electricity through photovoltaic panels."
        },
        {
          index: 1,
          question: "How does wind power work?",
          answer: "Wind power uses turbines to convert wind into electrical energy.",
          context: "Wind turbines capture kinetic energy from moving air and convert it to electricity through generators."
        }
      ]
    };

    const batchResponse = await fetch('http://localhost:3000/api/evaluation', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(batchData)
    });

    const batchResult = await batchResponse.json();
    
    console.log(`   Status: ${batchResponse.status}`);
    console.log(`   Total Requests: ${batchResult.total}`);
    console.log(`   Successful: ${batchResult.successful}`);
    console.log(`   Failed: ${batchResult.failed}`);
    console.log(`   Duration: ${batchResult.duration}ms`);
    
    if (batchResult.results && batchResult.results.length > 0) {
      batchResult.results.forEach((result: any, idx: number) => {
        console.log(`     Result ${idx + 1}: Score ${result.evaluation?.overall_score}/100`);
      });
    }
    
    console.log('   ✅ Batch evaluation API working\n');
  } catch (error) {
    console.log(`   ❌ Batch evaluation API failed: ${error}\n`);
  }

  // Test 5: Gemini API with Evaluation Integration
  console.log('🤖 Test 5: Gemini API with Evaluation Integration');
  try {
    // Mock selected records (simplified for testing)
    const mockRecords = [
      {
        fields: {
          ID: 1,
          Title: "Renewable Energy Benefits",
          "Cleaned Body": "Solar energy provides clean electricity without emissions. Wind power generates electricity from natural currents. Both help reduce greenhouse gas emissions.",
          "HTML URL": "https://example.com/renewable-energy",
          Country: "Global",
          "Section ID": "energy",
          Breadcrumb: "Energy > Renewable"
        }
      }
    ];

    const geminiData = {
      query: "What are the environmental benefits of renewable energy?",
      selectedRecords: mockRecords,
      includeEvaluation: true
    };

    const geminiResponse = await fetch('http://localhost:3000/api/gemini', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(geminiData)
    });

    const geminiResult = await geminiResponse.json();
    
    console.log(`   Status: ${geminiResponse.status}`);
    console.log(`   Response Generated: ${geminiResult.response ? 'Yes' : 'No'}`);
    console.log(`   Records Used: ${geminiResult.recordsUsed}`);
    console.log(`   Evaluation Included: ${geminiResult.evaluation ? 'Yes' : 'No'}`);
    
    if (geminiResult.evaluation) {
      console.log(`   Evaluation Score: ${geminiResult.evaluation.overall_score}/100`);
      console.log(`   Claims Analyzed: ${geminiResult.evaluation.evaluation_metadata?.total_claims}`);
    }
    
    if (geminiResult.evaluationError) {
      console.log(`   Evaluation Error: ${geminiResult.evaluationError}`);
    }
    
    console.log('   ✅ Gemini API with evaluation working\n');
  } catch (error) {
    console.log(`   ❌ Gemini API with evaluation failed: ${error}\n`);
  }

  // Test 6: Error Handling
  console.log('⚠️  Test 6: Error Handling');
  try {
    const invalidData = {
      question: "Test question",
      // Missing answer and context
    };

    const errorResponse = await fetch('http://localhost:3000/api/evaluation', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(invalidData)
    });

    const errorResult = await errorResponse.json();
    
    console.log(`   Status: ${errorResponse.status} (Expected: 400)`);
    console.log(`   Error Message: ${errorResult.error}`);
    console.log(`   Error Details: ${JSON.stringify(errorResult.details, null, 2)}`);
    console.log('   ✅ Error handling working correctly\n');
  } catch (error) {
    console.log(`   ❌ Error handling test failed: ${error}\n`);
  }

  console.log('🎉 Phase 2 Integration Testing Complete!');
  console.log('\n📋 Summary:');
  console.log('✅ Health check endpoint');
  console.log('✅ Metrics and monitoring');
  console.log('✅ Single evaluation API');
  console.log('✅ Batch evaluation API');
  console.log('✅ Gemini API integration');
  console.log('✅ Error handling');
  console.log('\n🚀 Phase 2: API Integration - COMPLETE');
}

// Direct execution for testing
if (require.main === module) {
  testPhase2Integration().catch(console.error);
}

export { testPhase2Integration }; 