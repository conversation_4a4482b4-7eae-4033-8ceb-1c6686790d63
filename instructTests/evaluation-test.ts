import { evaluateResponse } from '../lib/evaluation-utils';

/**
 * Test data for validation
 */
const testData = {
  question: "What are the benefits of renewable energy?",
  answer: "Renewable energy sources like solar and wind provide clean electricity, reduce carbon emissions, and create jobs in the green economy. They also help countries achieve energy independence.",
  mockRecords: [
    {
      fields: {
        ID: 1,
        Title: "Benefits of Solar Energy",
        "Cleaned Body": "Solar energy provides clean electricity without carbon emissions. It helps reduce dependency on fossil fuels and creates jobs in the renewable energy sector.",
        "HTML URL": "https://example.com/solar-benefits",
        Country: "Global",
        "Section ID": "renewable-energy",
        Breadcrumb: "Energy > Renewable > Solar"
      }
    },
    {
      fields: {
        ID: 2,
        Title: "Wind Energy Advantages", 
        "Cleaned Body": "Wind power is a clean energy source that generates electricity without pollution. Wind farms create employment opportunities in rural areas and contribute to energy security.",
        "HTML URL": "https://example.com/wind-advantages",
        Country: "Global",
        "Section ID": "renewable-energy",
        Breadcrumb: "Energy > Renewable > Wind"
      }
    }
  ]
};

/**
 * Run a test evaluation to validate the system
 */
export async function runTestEvaluation(): Promise<void> {
  try {
    console.log('🧪 Running evaluation system test...');
    console.log(`Question: ${testData.question}`);
    console.log(`Answer: ${testData.answer}`);
    console.log(`Records: ${testData.mockRecords.length} context records`);
    
    const startTime = Date.now();
    
    const result = await evaluateResponse(
      testData.question,
      testData.answer,
      testData.mockRecords
    );
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log('\n✅ Evaluation completed successfully!');
    console.log(`⏱️ Duration: ${duration}ms`);
    console.log(`📊 Overall Score: ${result.overall_score}/100`);
    console.log(`📝 Claims Found: ${result.evaluation_metadata.total_claims}`);
    console.log(`✅ Fully Supported: ${result.evaluation_metadata.fully_supported_claims}`);
    console.log(`⚠️ Partially Supported: ${result.evaluation_metadata.partially_supported_claims}`);
    console.log(`❌ Unsupported: ${result.evaluation_metadata.unsupported_claims}`);
    
    // Display individual claims
    console.log('\n📋 Claim Analysis:');
    result.claim_analysis.forEach((claim, index) => {
      const scoreIcon = claim.groundedness_score >= 70 ? '✅' : 
                       claim.groundedness_score >= 40 ? '⚠️' : '❌';
      console.log(`${scoreIcon} Claim ${index + 1} (Score: ${claim.groundedness_score}): ${claim.claim}`);
      console.log(`   Reasoning: ${claim.reasoning}`);
      console.log(`   Evidence: ${claim.supporting_context_snippets.length} snippets found`);
    });
    
  } catch (error) {
    console.error('❌ Test evaluation failed:', error);
    throw error;
  }
}

/**
 * Validate that all required components are working
 */
export async function validateEvaluationSystem(): Promise<{
  isValid: boolean;
  errors: string[];
  warnings: string[];
}> {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  try {
    // Check environment variables
    if (!process.env.GEMINI_API_KEY) {
      errors.push('GEMINI_API_KEY environment variable is not set');
    }
    
    // Test basic evaluation
    const result = await evaluateResponse(
      "Test question?",
      "Test answer with claims.",
      [{
        fields: {
          ID: 1,
          Title: "Test Record",
          "Cleaned Body": "Test content for evaluation.",
          "HTML URL": "https://test.com",
          Country: "Test",
          "Section ID": "test",
          Breadcrumb: "Test > Section"
        }
      }]
    );
    
    // Validate result structure
    if (!result.claim_analysis || !Array.isArray(result.claim_analysis)) {
      errors.push('Invalid evaluation result: missing claim_analysis array');
    }
    
    if (typeof result.overall_score !== 'number' || result.overall_score < 1 || result.overall_score > 100) {
      errors.push('Invalid overall_score: must be number between 1-100');
    }
    
    if (!result.evaluation_metadata) {
      errors.push('Invalid evaluation result: missing evaluation_metadata');
    }
    
    // Performance warning
    if (result.evaluation_metadata.total_claims === 0) {
      warnings.push('No claims were identified in the test answer');
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
    
  } catch (error) {
    errors.push(`Evaluation system test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    
    return {
      isValid: false,
      errors,
      warnings
    };
  }
} 