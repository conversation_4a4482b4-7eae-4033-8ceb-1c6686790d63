// Load environment variables for testing outside Next.js
import dotenv from 'dotenv';
import { resolve } from 'path';

// Load .env.local file if it exists
dotenv.config({ path: resolve(process.cwd(), '.env.local') });
dotenv.config({ path: resolve(process.cwd(), '.env') });

import { testHallucinationDetection } from './test-hallucination-detection';
import { testFakeEvidence } from './test-fake-evidence';

async function main() {
  console.log('🚀 Starting Hallucination Detection Test\n');
  
  try {
    // Check environment first
    if (!process.env.GEMINI_API_KEY) {
      console.error('❌ GEMINI_API_KEY environment variable is required');
      process.exit(1);
    }
    
    console.log('🔧 Environment: ✅ GEMINI_API_KEY is set');
    
    // Run the hallucination detection test
    await testHallucinationDetection();
    
    // Run the fake evidence test
    await testFakeEvidence();
    
    console.log('\n🎉 All hallucination detection tests completed successfully!');
    
  } catch (error) {
    console.error('\n💥 Test execution failed:', error instanceof Error ? error.message : error);
    if (error instanceof Error && error.stack) {
      console.error(error.stack);
    }
    process.exit(1);
  }
}

// Execute the test
main(); 