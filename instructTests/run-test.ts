// Load environment variables for testing outside Next.js
import dotenv from 'dotenv';
import { resolve } from 'path';

// Load .env.local file if it exists
dotenv.config({ path: resolve(process.cwd(), '.env.local') });
dotenv.config({ path: resolve(process.cwd(), '.env') });

import { runTestEvaluation, validateEvaluationSystem } from './evaluation-test';

async function main() {
  console.log('🚀 Starting Custom LLM Evaluation System Test\n');
  
  try {
    // Check environment first
    console.log(`🔧 Environment check:`)
    console.log(`  - GEMINI_API_KEY: ${process.env.GEMINI_API_KEY ? '✅ Set' : '❌ Not set'}`);
    console.log(`  - NODE_ENV: ${process.env.NODE_ENV || 'not set'}\n`);
    
    if (!process.env.GEMINI_API_KEY) {
      console.error('❌ GEMINI_API_KEY environment variable is required');
      console.error('   Please ensure .env.local or .env file contains GEMINI_API_KEY=your_key_here');
      process.exit(1);
    }
    
    // First validate the system
    console.log('🔍 Validating evaluation system...');
    const validation = await validateEvaluationSystem();
    
    if (!validation.isValid) {
      console.error('❌ System validation failed:');
      validation.errors.forEach(error => console.error(`  - ${error}`));
      process.exit(1);
    }
    
    if (validation.warnings.length > 0) {
      console.warn('⚠️ System validation warnings:');
      validation.warnings.forEach(warning => console.warn(`  - ${warning}`));
    }
    
    console.log('✅ System validation passed!\n');
    
    // Run the full test
    await runTestEvaluation();
    
    console.log('\n🎉 All tests completed successfully!');
    
  } catch (error) {
    console.error('\n💥 Test execution failed:', error instanceof Error ? error.message : error);
    if (error instanceof Error && error.stack) {
      console.error(error.stack);
    }
    process.exit(1);
  }
}

// Execute the test
main(); 