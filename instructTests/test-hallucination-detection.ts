import { getEvaluationService } from '../lib/evaluation-utils';
import type { EvaluationRequest } from '../lib/evaluation-service';

/**
 * Test hallucination detection by providing fake evidence
 */
export async function testHallucinationDetection(): Promise<void> {
  console.log('🔍 Testing hallucination detection...');
  
  const evaluationService = getEvaluationService();
  
  // Create a test that we'll manually validate with fake evidence
  const request: EvaluationRequest = {
    question: "What is renewable energy?",
    answer: "Renewable energy is clean and sustainable.",
    context: `Solar energy provides clean electricity without emissions. Wind power generates electricity from wind.`
  };
  
  try {
    // First, let's test the normal evaluation to see what real evidence looks like
    console.log('\n🧪 Step 1: Normal evaluation (should work)');
    const normalResult = await evaluationService.evaluateGroundedness(request);
    
    console.log(`Normal result - Overall Score: ${normalResult.overall_score}/100`);
    console.log(`Claims: ${normalResult.evaluation_metadata.total_claims}`);
    console.log(`Unsupported: ${normalResult.evaluation_metadata.unsupported_claims}`);
    
    // Show what real evidence looks like
    normalResult.claim_analysis.forEach((claim, index) => {
      console.log(`\nClaim ${index + 1} (Score: ${claim.groundedness_score}):`);
      console.log(`  Text: "${claim.claim}"`);
      console.log(`  Evidence snippets: ${claim.supporting_context_snippets.length}`);
      claim.supporting_context_snippets.forEach((snippet, i) => {
        console.log(`    ${i + 1}. Start: "${snippet.start_words}" | End: "${snippet.end_words}"`);
      });
    });
    
    console.log('\n✅ Normal evaluation completed successfully');
    
  } catch (error) {
    console.error('❌ Hallucination detection test failed:', error);
    throw error;
  }
} 