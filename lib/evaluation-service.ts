import { GoogleGenAI } from '@google/genai';

// Types for evaluation system
export interface ClaimAnalysis {
  claim: string;
  supporting_context_snippets: Array<{
    start_words: string;
    end_words: string;
  }>;
  reasoning: string;
  groundedness_score: number;
}

export interface EvaluationResult {
  claim_analysis: ClaimAnalysis[];
  overall_score: number;
  evaluation_metadata: {
    timestamp: string;
    total_claims: number;
    fully_supported_claims: number;
    partially_supported_claims: number;
    unsupported_claims: number;
  };
}

export interface EvaluationRequest {
  question: string;
  answer: string;
  context: string;
}

export class EvaluationService {
  private genAI: GoogleGenAI;

  constructor() {
    this.genAI = new GoogleGenAI({
      apiKey: process.env.GEMINI_API_KEY!,
    });
  }

  /**
   * Core evaluation prompt template
   */
  private getEvaluationPrompt(question: string, answer: string, context: string): string {
    return `## Primary Goal

You are an expert AI evaluator. Your purpose is to meticulously analyze a generated \`Answer\` and determine how well each of its claims is grounded in the provided \`Context\`. You will not evaluate the factual accuracy of the \`Context\` itself, but only whether the \`Answer\` is faithfully supported by it.

## Instructions

1.  **Identify Claims**: First, break down the \`Answer\` into a list of distinct, verifiable claims. A claim is a single, factual statement.
2.  **Analyze Each Claim**: For each individual claim, you must perform the following steps:
    - **Extract Evidence**: Thoroughly search the \`Context\` to identify the continuous text snippet(s) that act as evidence. For each snippet you identify, you must format it as follows:
      - Create an object containing \`start_words\` and \`end_words\`.
      - **\`start_words\`**: The first 5 words of the supporting snippet.
      - **\`end_words\`**: The last 5 words of the supporting snippet.
      - **Rule for short snippets**: If a snippet has 10 words or fewer, use the full snippet text for both the \`start_words\` and \`end_words\` fields.
      - If no supporting evidence is found, \`supporting_context_snippets\` should be an empty list \`[]\`.
    - **Assign Score**: Assign a \`groundedness_score\` from 1 to 100 for the claim based on its relationship to the \`Context\`. Use the following scoring rubric:
      - **Score 100**: The claim is a direct restatement or a perfect logical paraphrase of information stated explicitly in the \`Context\`.
      - **Score 70-99**: The claim is fully supported and a reasonable inference from the \`Context\`, but not stated verbatim.
      - **Score 40-69**: The claim is **partially supported**. The \`Context\` provides evidence for the general idea, but the claim is stronger or more specific than what is explicitly stated.
      - **Score 2-39**: The claim is **weakly related** to the \`Context\`, requiring a significant logical leap.
      - **Score 1**: The claim is **completely unsupported** by or directly **contradicts** the \`Context\`.
    - **Provide Reasoning**: Write a brief explanation for the score you assigned.
3.  **Format the Output**: Your final output MUST be a single JSON object containing the detailed analysis for all claims. Do not include any other text, notes, or explanations outside of this JSON structure.

## Question

${question}

## Answer

${answer}

## Context

${context}

## Evaluation Output (JSON)

Please provide your analysis in the following JSON format:

\`\`\`json
{
  "claim_analysis": [
    {
      "claim": "The first claim made in the answer.",
      "supporting_context_snippets": [
        {
          "start_words": "The first five words of",
          "end_words": "the last five words here."
        }
      ],
      "reasoning": "The claim is a direct restatement of the information found in the provided context, hence the perfect score.",
      "groundedness_score": 100
    }
  ]
}
\`\`\``;
  }

  /**
   * Evaluates the groundedness of an answer against provided context
   */
  async evaluateGroundedness(request: EvaluationRequest): Promise<EvaluationResult> {
    try {
      const prompt = this.getEvaluationPrompt(request.question, request.answer, request.context);
      
      // Generate content using Gemini 2.0 Flash
      const response = await this.genAI.models.generateContent({
        model: 'gemini-2.0-flash',
        contents: prompt,
        config: {
          temperature: 0.1, // Low temperature for consistent evaluation
          maxOutputTokens: 1536, // Reduced from 4096 for faster processing
        },
      });

      const responseText = response.text;
      
      if (!responseText) {
        throw new Error('Empty response from Gemini API');
      }
      
      // Parse the JSON response
      const claimAnalysis = this.parseEvaluationResponse(responseText);
      
      // Validate and process the response
      const validatedAnalysis = this.validateAndProcessClaims(claimAnalysis, request.context);
      
      // Calculate overall metrics
      const result = this.calculateOverallMetrics(validatedAnalysis);
      
      return result;
      
    } catch (error) {
      console.error('Evaluation service error:', error);
      throw new Error(`Evaluation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Parses the JSON response from the LLM
   */
  private parseEvaluationResponse(responseText: string): ClaimAnalysis[] {
    try {
      // Extract JSON from the response (in case it's wrapped in markdown)
      let jsonText = responseText;
      
      // Remove markdown code blocks if present
      const jsonMatch = responseText.match(/```json\s*([\s\S]*?)\s*```/);
      if (jsonMatch) {
        jsonText = jsonMatch[1];
      }
      
      const parsed = JSON.parse(jsonText);
      
      if (!parsed.claim_analysis || !Array.isArray(parsed.claim_analysis)) {
        throw new Error('Invalid response format: missing claim_analysis array');
      }
      
      return parsed.claim_analysis;
      
    } catch (error) {
      console.error('Failed to parse evaluation response:', error);
      throw new Error('Failed to parse evaluation response from LLM');
    }
  }

  /**
   * Validates and processes claim analysis results
   */
  private validateAndProcessClaims(claims: ClaimAnalysis[], context: string): ClaimAnalysis[] {
    return claims.map((claim, index) => {
      // Validate required fields
      if (!claim.claim || typeof claim.claim !== 'string') {
        console.warn(`Claim ${index} missing or invalid claim text`);
        claim.claim = claim.claim || `Claim ${index + 1}`;
      }
      
      if (!claim.reasoning || typeof claim.reasoning !== 'string') {
        console.warn(`Claim ${index} missing reasoning`);
        claim.reasoning = 'No reasoning provided';
      }
      
      // Validate score range
      if (typeof claim.groundedness_score !== 'number' || 
          claim.groundedness_score < 1 || 
          claim.groundedness_score > 100) {
        console.warn(`Claim ${index} has invalid score: ${claim.groundedness_score}`);
        claim.groundedness_score = 1; // Default to lowest score for invalid responses
      }
      
      // Validate supporting context snippets
      if (!Array.isArray(claim.supporting_context_snippets)) {
        console.warn(`Claim ${index} has invalid supporting_context_snippets`);
        claim.supporting_context_snippets = [];
      }
      
      // Validate each snippet and check for hallucination
      let hasValidSnippets = true;
      claim.supporting_context_snippets = claim.supporting_context_snippets.map(snippet => {
        if (!snippet.start_words || !snippet.end_words) {
          console.warn(`Invalid snippet format in claim ${index}`);
          hasValidSnippets = false;
          return {
            start_words: snippet.start_words || '',
            end_words: snippet.end_words || ''
          };
        }
        
        // Attempt programmatic extraction to verify snippet exists
        const extractedSnippet = this.extractSnippetFromContext(context, snippet.start_words, snippet.end_words);
        if (!extractedSnippet) {
          console.warn(`Failed to extract snippet for claim ${index}: "${snippet.start_words}...${snippet.end_words}"`);
          hasValidSnippets = false;
        }
        
        return snippet;
      });
      
      // If any snippet extraction failed, mark as hallucination
      if (claim.supporting_context_snippets.length > 0 && !hasValidSnippets) {
        console.warn(`Marking claim ${index} as hallucination due to failed snippet extraction`);
        claim.groundedness_score = 0;
        claim.reasoning = 'Likely hallucination: Supporting evidence could not be found in the provided context';
      }
      
      return claim;
    });
  }

  /**
   * Attempts to extract a snippet from context using start and end words
   */
  private extractSnippetFromContext(context: string, startWords: string, endWords: string): string | null {
    try {
      // Handle short snippets (≤10 words) where start and end words are the same
      if (startWords === endWords) {
        const words = startWords.trim().split(/\s+/);
        if (words.length <= 10) {
          // For short snippets, just check if the full text exists in context
          const regex = new RegExp(this.escapeRegex(startWords), 'i');
          return regex.test(context) ? startWords : null;
        }
      }
      
      // Clean and normalize the search terms
      const cleanStartWords = startWords.trim().toLowerCase();
      const cleanEndWords = endWords.trim().toLowerCase();
      const cleanContext = context.toLowerCase();
      
      // Find the position of start words
      const startIndex = cleanContext.indexOf(cleanStartWords);
      if (startIndex === -1) {
        return null;
      }
      
      // Find the position of end words, searching from the start position
      const endIndex = cleanContext.indexOf(cleanEndWords, startIndex);
      if (endIndex === -1) {
        return null;
      }
      
      // Extract the snippet from original context (preserve case)
      const snippetStart = startIndex;
      const snippetEnd = endIndex + endWords.length;
      
      if (snippetEnd <= snippetStart) {
        return null;
      }
      
      // Extract from original context to preserve formatting
      const snippet = context.substring(snippetStart, snippetEnd);
      
      // Validate that the snippet is reasonable (not too long, not empty)
      if (snippet.length > 1000 || snippet.trim().length === 0) {
        return null;
      }
      
      return snippet.trim();
      
    } catch (error) {
      console.error('Error extracting snippet:', error);
      return null;
    }
  }

  /**
   * Escapes special regex characters
   */
  private escapeRegex(text: string): string {
    return text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  /**
   * Calculates overall evaluation metrics
   */
  private calculateOverallMetrics(claims: ClaimAnalysis[]): EvaluationResult {
    const totalClaims = claims.length;
    const hallucinationClaims = claims.filter(c => c.groundedness_score === 0).length;
    const fullySupportedClaims = claims.filter(c => c.groundedness_score >= 70).length;
    const partiallySupportedClaims = claims.filter(c => c.groundedness_score >= 40 && c.groundedness_score < 70).length;
    const unsupportedClaims = claims.filter(c => c.groundedness_score > 0 && c.groundedness_score < 40).length;
    
    // Calculate overall score as weighted average
    const overallScore = totalClaims > 0 
      ? Math.round(claims.reduce((sum, claim) => sum + claim.groundedness_score, 0) / totalClaims)
      : 0;
    
    return {
      claim_analysis: claims,
      overall_score: overallScore,
      evaluation_metadata: {
        timestamp: new Date().toISOString(),
        total_claims: totalClaims,
        fully_supported_claims: fullySupportedClaims,
        partially_supported_claims: partiallySupportedClaims,
        unsupported_claims: unsupportedClaims + hallucinationClaims, // Include hallucinations in unsupported
      }
    };
  }

  /**
   * Health check for the evaluation service
   */
  async healthCheck(): Promise<{ status: string; timestamp: string }> {
    try {
      // Simple test to verify Gemini API is accessible
      const response = await this.genAI.models.generateContent({
        model: 'gemini-2.0-flash',
        contents: 'Test connection',
        config: {
          temperature: 0.1,
          maxOutputTokens: 10,
        },
      });
      
      return {
        status: 'healthy',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Health check failed:', error);
      return {
        status: 'unhealthy',
        timestamp: new Date().toISOString()
      };
    }
  }
}