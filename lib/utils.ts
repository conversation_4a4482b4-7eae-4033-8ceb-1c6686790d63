import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength).trim() + "..."
}

export function getInitials(name: string): string {
  return name
    .split(" ")
    .map((word) => word.charAt(0))
    .join("")
    .toUpperCase()
    .substring(0, 2)
}

export function formatScore(score: number): string {
  return score.toFixed(1)
}

export function getBM42ScoreColor(score: number): string {
  if (score >= 8) return "bg-green-500"
  if (score >= 6) return "bg-yellow-500"
  return "bg-red-500"
}

export function getMethodBadgeVariant(method: string): "default" | "secondary" | "outline" {
  switch (method) {
    case "bm42":
      return "default"
    case "text-search":
      return "secondary"
    case "urls":
      return "outline"
    default:
      return "secondary"
  }
}
