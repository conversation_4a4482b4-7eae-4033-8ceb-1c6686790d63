import type { Node, Record } from "@/types"

interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
}

interface DataResponse {
  nodes: Node[]
  records: Record[]
}

export async function fetchData(): Promise<DataResponse> {
  const response = await fetch('/api/data')
  
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`)
  }
  
  const result: ApiResponse<DataResponse> = await response.json()
  
  if (result.success && result.data) {
    return result.data
  } else {
    throw new Error(result.error || 'Failed to load data')
  }
} 