import { EvaluationService, EvaluationRequest, EvaluationResult } from './evaluation-service';

// Singleton instance for reuse across the application
let evaluationServiceInstance: EvaluationService | null = null;

/**
 * Get a singleton instance of the evaluation service
 */
export function getEvaluationService(): EvaluationService {
  if (!evaluationServiceInstance) {
    evaluationServiceInstance = new EvaluationService();
  }
  return evaluationServiceInstance;
}

/**
 * Helper function to evaluate a response with simplified interface
 */
export async function evaluateResponse(
  question: string,
  answer: string,
  contextRecords: any[]
): Promise<EvaluationResult> {
  const evaluationService = getEvaluationService();
  
  // Format context from records (similar to existing Gemini API)
  const context = contextRecords.map((record, index) => {
    return `Record ${index + 1}:
Title: ${record.fields.Title || `Record #${record.fields.ID}`}
Content: ${record.fields["Cleaned Body"]}
Source: ${record.fields["HTML URL"]}
Country: ${record.fields.Country}
Section: ${record.fields["Section ID"]}
Breadcrumb: ${record.fields.Breadcrumb}
---`;
  }).join('\n\n');

  const request: EvaluationRequest = {
    question,
    answer,
    context
  };

  return await evaluationService.evaluateGroundedness(request);
}

/**
 * Helper function to format evaluation results for display
 */
export function formatEvaluationSummary(result: EvaluationResult): string {
  const { overall_score, evaluation_metadata } = result;
  
  let quality = 'Poor';
  if (overall_score >= 70) quality = 'Good';
  else if (overall_score >= 40) quality = 'Fair';
  
  return `Overall Score: ${overall_score}/100 (${quality})
Claims Analysis: ${evaluation_metadata.total_claims} total claims
✅ Fully Supported: ${evaluation_metadata.fully_supported_claims}
⚠️ Partially Supported: ${evaluation_metadata.partially_supported_claims}
❌ Unsupported: ${evaluation_metadata.unsupported_claims}`;
}

/**
 * Helper function to get score color for UI display
 */
export function getScoreColor(score: number): string {
  if (score >= 70) return 'text-green-600 bg-green-50';
  if (score >= 40) return 'text-yellow-600 bg-yellow-50';
  return 'text-red-600 bg-red-50';
}

/**
 * Helper function to get score badge variant
 */
export function getScoreBadgeVariant(score: number): 'default' | 'secondary' | 'destructive' {
  if (score >= 70) return 'default';
  if (score >= 40) return 'secondary';
  return 'destructive';
}

/**
 * Create evaluation request from existing Gemini API parameters
 */
export function createEvaluationRequest(
  geminiRequestBody: { query: string; selectedRecords: any[] },
  aiResponse: string
): {
  question: string;
  answer: string;
  contextRecords: any[];
} {
  return {
    question: geminiRequestBody.query,
    answer: aiResponse,
    contextRecords: geminiRequestBody.selectedRecords
  };
} 