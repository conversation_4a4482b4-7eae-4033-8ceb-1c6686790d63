import type { Node, Record } from "@/types"

interface JsonNode {
  children: { [key: string]: JsonNode }
  records: Array<{
    id: string
    fields: {
      ID: number
      "HTML URL": string
      Title: string
      "Cleaned Body": string
      Breadcrumb: string
      "Is Deepest": string
      Country: string
      "Section ID": number
    }
  }>
  id: string
  country: string
  summary?: string
  refinedSummary?: string
}

interface JsonData {
  children: { [key: string]: JsonNode }
}

function transformJsonToNodes(jsonData: JsonData, basePath = ""): { nodes: Node[]; records: Record[] } {
  const nodes: Node[] = []
  const allRecords: Record[] = []

  for (const [nodeTitle, nodeData] of Object.entries(jsonData.children)) {
    const currentPath = basePath ? `${basePath}/${nodeTitle}` : nodeTitle

    // Transform records for this node
    const nodeRecords: Record[] = nodeData.records.map((record) => ({
      ...record,
      _bm42_score: Math.random() * 10, // Generate random score for demo
      _search_method: Math.random() > 0.7 ? "bm42" : Math.random() > 0.5 ? "text-search" : ("urls" as const),
      _node_path: currentPath,
      fields: {
        ...record.fields,
        "Cleaned Body": record.fields["Cleaned Body"] || "No content available",
      },
    }))

    // Recursively process children
    const childResult = transformJsonToNodes({ children: nodeData.children }, currentPath)
    const childNodes = childResult.nodes
    const childRecords = childResult.records

    // Calculate total record count (this node + all descendants)
    const totalRecordCount = nodeRecords.length + childRecords.length

    // Create the node
    const node: Node = {
      id: nodeData.id,
      path: currentPath,
      country: nodeData.country,
      summary: nodeData.summary || `Documentation for ${nodeTitle}`,
      refined_summary:
        nodeData.refinedSummary || nodeData.summary || `Comprehensive documentation and guides for ${nodeTitle}`,
      record_count: totalRecordCount,
      has_children: Object.keys(nodeData.children).length > 0,
      children: childNodes,
      has_records: nodeRecords.length > 0,
    }

    nodes.push(node)
    allRecords.push(...nodeRecords, ...childRecords)
  }

  return { nodes, records: allRecords }
}

// Updated mock data structure with additional nested item
const mockJsonData: JsonData = {
  children: {
    "Docs Team [Internal]": {
      children: {
        "Draft Article by AI": {
          children: {},
          records: [
            {
              id: "recrLpy9zxXijhomG",
              fields: {
                ID: **************,
                "HTML URL":
                  "https://help.xendit.co/hc/en-us/articles/**************-Cards-Chargeback-Case-Handling-Philippines",
                Title: "Cards - Chargeback Case Handling - Philippines",
                "Cleaned Body":
                  "## Typical Customer Issues\n* Merchants may inquire about the status of chargeback cases.\n* Requests for additional documentation or evidence needed to support chargeback disputes.\n* Questions regarding the automation process for chargeback handling.\n\n## Probe\n* Gather the transaction ID related to the chargeback.\n* Request any additional details from the customer that may help in processing the chargeback.\n* Verify if there's relevant documentation that the merchant has submitted for the dispute.",
                Breadcrumb: "Category: Docs Team [Internal] // Section: Draft Article by AI",
                "Is Deepest": "true",
                Country: "general",
                "Section ID": **************,
              },
            },
            {
              id: "recr5zuNdxBeDpzoY",
              fields: {
                ID: **************,
                "HTML URL":
                  "https://help.xendit.co/hc/en-us/articles/**************-Withdrawal-Process-Overview-for-Customer-Success",
                Title: "Withdrawal Process Overview for Customer Success",
                "Cleaned Body":
                  "This guide provides an overview of the withdrawal process for customer success teams. It covers the standard procedures, common issues, and escalation paths when handling withdrawal requests from merchants.",
                Breadcrumb: "Category: Docs Team [Internal] // Section: Draft Article by AI",
                "Is Deepest": "true",
                Country: "general",
                "Section ID": **************,
              },
            },
            {
              id: "rectba6hZtnw1AiAE",
              fields: {
                ID: 46515446660889,
                "HTML URL":
                  "https://help.xendit.co/hc/en-us/articles/46515446660889-Dashboard-Issues-Mapping-and-Escalation-Process",
                Title: "Dashboard Issues Mapping and Escalation Process",
                "Cleaned Body":
                  "This document outlines the process for mapping dashboard issues and the appropriate escalation procedures. It includes troubleshooting steps, common error scenarios, and when to involve technical teams.",
                Breadcrumb: "Category: Docs Team [Internal] // Section: Draft Article by AI",
                "Is Deepest": "true",
                Country: "general",
                "Section ID": **************,
              },
            },
          ],
          id: "ba9233fa-39a8-4edc-8112-0b220a3197dc",
          country: "general",
          summary: "AI-generated draft articles for internal documentation",
          refinedSummary:
            "Draft Article by AI covers troubleshooting and resolving issues related to payment processing (including manual OTC payments via Cebuana, failed e-wallet transactions with Trojans Well, and QRPH transactions), merchant onboarding, Xendit Checkout integration with Cloudbeds, disbursement failures (including bouncebacks and UBP Connectors like Instapay and Pesonet), and merchant inquiries about topics like Bill Payment, Withholding Tax Slips, subscription plans, and accessing old data reports, often involving Admin Dashboard checks, Retool applications, and escalation to internal teams like Merchant Risk.",
        },
        "Customer Support Guidelines": {
          children: {},
          records: [
            {
              id: "rec_cs_001",
              fields: {
                ID: 47233790870042,
                "HTML URL": "https://help.xendit.co/hc/en-us/articles/customer-support-escalation",
                Title: "Customer Support Escalation Matrix",
                "Cleaned Body":
                  "This document defines the escalation matrix for customer support issues. It outlines when to escalate to different teams based on issue severity, customer tier, and technical complexity.",
                Breadcrumb: "Category: Docs Team [Internal] // Section: Customer Support Guidelines",
                "Is Deepest": "true",
                Country: "general",
                "Section ID": 41880529878426,
              },
            },
          ],
          id: "cs-guidelines-001",
          country: "general",
          summary: "Guidelines and procedures for customer support teams",
          refinedSummary:
            "Comprehensive guidelines for customer support teams including escalation procedures, response time requirements, and best practices for handling various types of customer inquiries.",
        },
      },
      records: [
        {
          id: "rec_internal_001",
          fields: {
            ID: 47233790870040,
            "HTML URL": "https://help.xendit.co/hc/en-us/articles/internal-team-overview",
            Title: "Internal Team Documentation Overview",
            "Cleaned Body":
              "This is the main overview document for all internal team documentation. It provides an index of available resources and guidelines for contributing to the knowledge base.",
            Breadcrumb: "Category: Docs Team [Internal]",
            "Is Deepest": "false",
            Country: "general",
            "Section ID": **************,
          },
        },
      ],
      id: "6485c9fa-e5f0-4b32-90c2-89d8ed283c3b",
      country: "general",
      summary: "Internal documentation and guidelines",
      refinedSummary:
        "Internal documentation hub containing guidelines, procedures, and resources for internal teams including customer support, technical documentation, and operational procedures.",
    },
    "Payment Processing": {
      children: {
        "Credit Card": {
          children: {
            "Visa Integration": {
              children: {},
              records: [
                {
                  id: "rec_visa_001",
                  fields: {
                    ID: **************,
                    "HTML URL": "https://help.xendit.co/hc/en-us/articles/visa-direct-api-integration",
                    Title: "Visa Direct API Integration Guide",
                    "Cleaned Body":
                      "Complete guide for integrating Visa Direct API for real-time payments. Covers authentication, endpoint configuration, error handling, and testing procedures.",
                    Breadcrumb: "Category: Payment Processing // Section: Credit Card // Subsection: Visa Integration",
                    "Is Deepest": "true",
                    Country: "global",
                    "Section ID": **************,
                  },
                },
              ],
              id: "visa-integration-001",
              country: "global",
              summary: "Visa payment integration documentation",
              refinedSummary:
                "Detailed implementation guides for Visa payment processing including Direct API integration, webhook handling, and fraud prevention tools.",
            },
          },
          records: [
            {
              id: "rec_cc_001",
              fields: {
                ID: **************,
                "HTML URL": "https://help.xendit.co/hc/en-us/articles/credit-card-setup",
                Title: "Credit Card Processing Setup",
                "Cleaned Body":
                  "How to set up credit card processing for your application. Credit card processing requires merchant account configuration with your payment processor. Begin by obtaining API credentials from your payment gateway, then implement secure token handling for sensitive card data.",
                Breadcrumb: "Category: Payment Processing // Section: Credit Card",
                "Is Deepest": "false",
                Country: "global",
                "Section ID": **************,
              },
            },
          ],
          id: "credit-card-001",
          country: "global",
          summary: "Credit card payment processing guides",
          refinedSummary:
            "Comprehensive documentation for implementing credit card payment processing, including security protocols, PCI compliance, transaction flows, and integration examples for major payment processors.",
        },
      },
      records: [],
      id: "payment-processing-001",
      country: "global",
      summary: "Payment processing documentation",
      refinedSummary:
        "Complete payment processing documentation covering various payment methods, integration guides, security requirements, and best practices for handling financial transactions.",
    },
    "API Documentation": {
      children: {
        "REST APIs": {
          children: {
            Authentication: {
              children: {
                "OAuth 2.0": {
                  children: {},
                  records: [
                    {
                      id: "rec_oauth_001",
                      fields: {
                        ID: **************,
                        "HTML URL": "https://help.xendit.co/hc/en-us/articles/oauth-client-credentials-flow",
                        Title: "OAuth 2.0 Client Credentials Flow Implementation",
                        "Cleaned Body":
                          "Detailed guide for implementing OAuth 2.0 Client Credentials Flow for server-to-server authentication. This flow is ideal for backend services that need to authenticate without user interaction. Includes code examples, security considerations, and token management best practices.",
                        Breadcrumb:
                          "Category: API Documentation // Section: REST APIs // Subsection: Authentication // OAuth 2.0",
                        "Is Deepest": "true",
                        Country: "global",
                        "Section ID": 41880529878460,
                      },
                    },
                    {
                      id: "rec_oauth_002",
                      fields: {
                        ID: 47233790870061,
                        "HTML URL": "https://help.xendit.co/hc/en-us/articles/oauth-authorization-code-flow",
                        Title: "OAuth 2.0 Authorization Code Flow for Web Applications",
                        "Cleaned Body":
                          "Complete implementation guide for OAuth 2.0 Authorization Code Flow in web applications. Covers PKCE implementation, state parameter usage, token refresh mechanisms, and security best practices for protecting user data.",
                        Breadcrumb:
                          "Category: API Documentation // Section: REST APIs // Subsection: Authentication // OAuth 2.0",
                        "Is Deepest": "true",
                        Country: "global",
                        "Section ID": 41880529878460,
                      },
                    },
                  ],
                  id: "oauth-2-001",
                  country: "global",
                  summary: "OAuth 2.0 authentication implementation guides",
                  refinedSummary:
                    "Comprehensive OAuth 2.0 implementation documentation covering all major flows including Client Credentials, Authorization Code, and PKCE. Includes security best practices, token management, and real-world integration examples.",
                },
                "JWT Tokens": {
                  children: {},
                  records: [
                    {
                      id: "rec_jwt_001",
                      fields: {
                        ID: 47233790870062,
                        "HTML URL": "https://help.xendit.co/hc/en-us/articles/jwt-token-validation",
                        Title: "JWT Token Validation and Security Best Practices",
                        "Cleaned Body":
                          "Essential guide for JWT token validation including signature verification, claims validation, and security considerations. Covers common vulnerabilities like algorithm confusion attacks and provides implementation examples for secure JWT handling.",
                        Breadcrumb:
                          "Category: API Documentation // Section: REST APIs // Subsection: Authentication // JWT Tokens",
                        "Is Deepest": "true",
                        Country: "global",
                        "Section ID": 41880529878461,
                      },
                    },
                  ],
                  id: "jwt-tokens-001",
                  country: "global",
                  summary: "JWT token implementation and security guides",
                  refinedSummary:
                    "Complete JWT token documentation covering token generation, validation, security best practices, and common pitfalls. Includes examples for multiple programming languages and frameworks.",
                },
              },
              records: [
                {
                  id: "rec_auth_overview_001",
                  fields: {
                    ID: 47233790870059,
                    "HTML URL": "https://help.xendit.co/hc/en-us/articles/api-authentication-overview",
                    Title: "API Authentication Methods Overview",
                    "Cleaned Body":
                      "Comprehensive overview of available API authentication methods including API keys, OAuth 2.0, JWT tokens, and basic authentication. This guide helps developers choose the right authentication method based on their use case, security requirements, and application architecture.",
                    Breadcrumb: "Category: API Documentation // Section: REST APIs // Subsection: Authentication",
                    "Is Deepest": "false",
                    Country: "global",
                    "Section ID": 41880529878458,
                  },
                },
                {
                  id: "rec_auth_security_001",
                  fields: {
                    ID: 47233790870063,
                    "HTML URL": "https://help.xendit.co/hc/en-us/articles/api-security-checklist",
                    Title: "API Security Checklist for Authentication",
                    "Cleaned Body":
                      "Essential security checklist for API authentication including HTTPS enforcement, rate limiting, token expiration, secure storage practices, and monitoring. This checklist ensures your API authentication implementation follows industry security standards.",
                    Breadcrumb: "Category: API Documentation // Section: REST APIs // Subsection: Authentication",
                    "Is Deepest": "false",
                    Country: "global",
                    "Section ID": 41880529878458,
                  },
                },
              ],
              id: "api-auth-001",
              country: "global",
              summary: "API authentication methods and implementation guides",
              refinedSummary:
                "Complete API authentication documentation covering OAuth 2.0, JWT tokens, API keys, and security best practices. Includes implementation guides, security checklists, and real-world examples for secure API access control.",
            },
          },
          records: [
            {
              id: "rec_rest_overview_001",
              fields: {
                ID: 47233790870058,
                "HTML URL": "https://help.xendit.co/hc/en-us/articles/rest-api-design-principles",
                Title: "REST API Design Principles and Best Practices",
                "Cleaned Body":
                  "Fundamental guide to REST API design covering resource naming conventions, HTTP methods usage, status codes, versioning strategies, and documentation standards. Essential reading for developers building RESTful services.",
                Breadcrumb: "Category: API Documentation // Section: REST APIs",
                "Is Deepest": "false",
                Country: "global",
                "Section ID": 41880529878457,
              },
            },
          ],
          id: "rest-apis-001",
          country: "global",
          summary: "REST API design and implementation documentation",
          refinedSummary:
            "Comprehensive REST API documentation covering design principles, authentication methods, error handling, versioning, and best practices for building scalable and maintainable RESTful services.",
        },
      },
      records: [],
      id: "api-documentation-001",
      country: "global",
      summary: "Complete API documentation and integration guides",
      refinedSummary:
        "Comprehensive API documentation hub covering REST APIs, GraphQL, webhooks, authentication methods, and integration guides. Includes code examples, best practices, and troubleshooting guides for developers.",
    },
  },
}

export async function loadMockData(): Promise<{ nodes: Node[]; records: Record[] }> {
  try {
    // Simulate async loading
    await new Promise((resolve) => setTimeout(resolve, 500))
    return transformJsonToNodes(mockJsonData)
  } catch (error) {
    console.error("Failed to load mock data:", error)
    return { nodes: [], records: [] }
  }
}

// Keep the old exports for backward compatibility, but they'll be empty
export const mockNodes: Node[] = []
export const mockRecords: Record[] = []
