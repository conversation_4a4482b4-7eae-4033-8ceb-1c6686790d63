import { FalkorDB } from 'falkordb';

// Load environment variables explicitly for better reliability
if (typeof window === 'undefined') {
  // Only load dotenv on server side
  try {
    require('dotenv').config({ path: '.env.local' });
  } catch (e) {
    // dotenv not available, rely on Next.js built-in env loading
  }
}

// FalkorDB configuration
const FALKOR_CONFIG = {
  host: process.env.FALKOR_HOST || '127.0.0.1',
  port: parseInt(process.env.FALKOR_PORT || '6379'),
  password: process.env.FALKOR_PASSWORD,
  username: process.env.FALKOR_USERNAME,
  // FalkorDB cloud connection string (if provided)
  connectionString: process.env.FALKOR_CONNECTION_STRING,
};

// Debug logging (remove in production)
if (process.env.NODE_ENV === 'development') {
  console.log('FalkorDB Config:', {
    host: FALKOR_CONFIG.host,
    port: FALKOR_CONFIG.port,
    hasUsername: !!FALKOR_CONFIG.username,
    hasPassword: !!FALKOR_CONFIG.password,
    hasConnectionString: !!FALKOR_CONFIG.connectionString
  });
}

// Global connection instance
let falkorClient: FalkorDB | null = null;

/**
 * Get or create FalkorDB connection
 */
export async function getFalkorConnection(): Promise<FalkorDB> {
  if (!falkorClient) {
    try {
      // Use connection string if provided (for FalkorDB Cloud)
      if (FALKOR_CONFIG.connectionString) {
        falkorClient = await FalkorDB.connect({
          url: FALKOR_CONFIG.connectionString
        });
      } else {
        // Use individual connection parameters
        const connectionOptions: any = {
          socket: {
            host: FALKOR_CONFIG.host,
            port: FALKOR_CONFIG.port,
          }
        };

        // Add authentication if provided
        if (FALKOR_CONFIG.username) {
          connectionOptions.username = FALKOR_CONFIG.username;
        }
        if (FALKOR_CONFIG.password) {
          connectionOptions.password = FALKOR_CONFIG.password;
        }

        falkorClient = await FalkorDB.connect(connectionOptions);
      }

      console.log('FalkorDB connection established successfully');
    } catch (error) {
      console.error('Failed to connect to FalkorDB:', error);
      throw new Error('FalkorDB connection failed');
    }
  }

  return falkorClient;
}

/**
 * Close FalkorDB connection
 */
export async function closeFalkorConnection(): Promise<void> {
  if (falkorClient) {
    try {
      await falkorClient.close();
      falkorClient = null;
      console.log('FalkorDB connection closed');
    } catch (error) {
      console.error('Error closing FalkorDB connection:', error);
    }
  }
}

/**
 * Create a new graph in FalkorDB
 */
export async function createGraph(graphName: string): Promise<boolean> {
  try {
    const client = await getFalkorConnection();
    const graph = client.selectGraph(graphName);

    // Create a simple node to initialize the graph
    const query = 'CREATE (n:Graph {name: $name, created_at: $timestamp})';

    await graph.query(query, {
      params: {
        name: graphName,
        timestamp: new Date().toISOString()
      }
    });
    console.log(`Graph '${graphName}' created successfully`);
    return true;
  } catch (error) {
    console.error(`Error creating graph '${graphName}':`, error);
    throw error;
  }
}

/**
 * Check if a graph exists
 */
export async function graphExists(graphName: string): Promise<boolean> {
  try {
    const client = await getFalkorConnection();
    const graphs = await client.list();
    return graphs.includes(graphName);
  } catch (error) {
    console.error(`Error checking if graph '${graphName}' exists:`, error);
    return false;
  }
}

/**
 * Delete a graph
 */
export async function deleteGraph(graphName: string): Promise<boolean> {
  try {
    const client = await getFalkorConnection();
    const graph = client.selectGraph(graphName);
    await graph.delete();
    console.log(`Graph '${graphName}' deleted successfully`);
    return true;
  } catch (error) {
    console.error(`Error deleting graph '${graphName}':`, error);
    throw error;
  }
}

/**
 * Get graph statistics
 */
export async function getGraphStats(graphName: string): Promise<any> {
  try {
    const client = await getFalkorConnection();
    const graph = client.selectGraph(graphName);

    const nodeCountQuery = 'MATCH (n) RETURN count(n) as nodeCount';
    const edgeCountQuery = 'MATCH ()-[r]->() RETURN count(r) as edgeCount';

    const [nodeResult, edgeResult] = await Promise.all([
      graph.query(nodeCountQuery),
      graph.query(edgeCountQuery)
    ]);

    // Handle the result structure properly
    const nodeCount = (nodeResult?.data as any)?.[0]?.nodeCount || 0;
    const edgeCount = (edgeResult?.data as any)?.[0]?.edgeCount || 0;

    return {
      nodeCount,
      edgeCount,
      graphName
    };
  } catch (error) {
    console.error(`Error getting stats for graph '${graphName}':`, error);
    throw error;
  }
}

/**
 * Execute a custom query on a graph
 */
export async function executeQuery(
  graphName: string,
  query: string,
  params: Record<string, any> = {}
): Promise<any> {
  try {
    const client = await getFalkorConnection();
    const graph = client.selectGraph(graphName);

    const result = await graph.query(query, { params });
    return result;
  } catch (error) {
    console.error(`Error executing query on graph '${graphName}':`, error);
    throw error;
  }
}

/**
 * Health check for FalkorDB connection
 */
export async function healthCheck(): Promise<{ status: string; message: string }> {
  try {
    const client = await getFalkorConnection();
    // Test connection by listing graphs
    await client.list();
    return { status: 'healthy', message: 'FalkorDB connection is working' };
  } catch (error) {
    return {
      status: 'unhealthy',
      message: `FalkorDB connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}
