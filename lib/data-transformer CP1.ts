import { executeQuery } from './falkordb';
import type { Node, Record } from '@/types';

// Types for the existing JSON structure
interface JsonRecord {
  id: string;
  fields: {
    ID: number;
    "HTML URL": string;
    Title: string;
    Breadcrumb: string;
    "Is Deepest": string;
    Country: string;
    "Section ID": number;
    "Cleaned Body": string;
  };
}

interface JsonNode {
  id: string;
  country: string;
  summary: string;
  refinedSummary: string;
  children: Record<string, JsonNode>;
  records: JsonRecord[];
}

interface JsonData {
  children: Record<string, JsonNode>;
}

// Types for FalkorDB nodes
interface CategoryNode {
  id: string;
  name: string;
  country: string;
  summary: string;
  refinedSummary: string;
  nodeType: 'category';
  level: number;
  path: string;
}

interface RecordNode {
  id: string;
  recordId: number;
  title: string;
  htmlUrl: string;
  breadcrumb: string;
  isDeepest: boolean;
  country: string;
  sectionId: number;
  cleanedBody: string;
  nodeType: 'record';
}

/**
 * Transform JSON data structure to FalkorDB nodes and relationships
 */
export function transformJsonToGraph(jsonData: JsonData): {
  categoryNodes: CategoryNode[];
  recordNodes: RecordNode[];
  relationships: Array<{ from: string; to: string; type: string }>;
} {
  const categoryNodes: CategoryNode[] = [];
  const recordNodes: RecordNode[] = [];
  const relationships: Array<{ from: string; to: string; type: string }> = [];

  function processNode(
    name: string,
    node: JsonNode,
    level: number = 0,
    parentPath: string = '',
    parentId?: string
  ) {
    const currentPath = parentPath ? `${parentPath}/${name}` : name;
    
    // Create category node
    const categoryNode: CategoryNode = {
      id: node.id,
      name,
      country: node.country,
      summary: node.summary,
      refinedSummary: node.refinedSummary,
      nodeType: 'category',
      level,
      path: currentPath
    };
    
    categoryNodes.push(categoryNode);
    
    // Create relationship to parent if exists
    if (parentId) {
      relationships.push({
        from: parentId,
        to: node.id,
        type: 'HAS_CHILD'
      });
    }
    
    // Process records
    node.records.forEach(record => {
      const recordNode: RecordNode = {
        id: record.id,
        recordId: record.fields.ID,
        title: record.fields.Title,
        htmlUrl: record.fields["HTML URL"],
        breadcrumb: record.fields.Breadcrumb,
        isDeepest: record.fields["Is Deepest"] === "true",
        country: record.fields.Country,
        sectionId: record.fields["Section ID"],
        cleanedBody: record.fields["Cleaned Body"],
        nodeType: 'record'
      };
      
      recordNodes.push(recordNode);
      
      // Create relationship from category to record
      relationships.push({
        from: node.id,
        to: record.id,
        type: 'CONTAINS'
      });
    });
    
    // Process children recursively
    Object.entries(node.children).forEach(([childName, childNode]) => {
      processNode(childName, childNode, level + 1, currentPath, node.id);
    });
  }

  // Process root level nodes
  Object.entries(jsonData.children).forEach(([name, node]) => {
    processNode(name, node);
  });

  return { categoryNodes, recordNodes, relationships };
}

/**
 * Create category nodes in FalkorDB
 */
export async function createCategoryNodes(
  graphName: string,
  categoryNodes: CategoryNode[]
): Promise<void> {
  try {
    for (const node of categoryNodes) {
      const query = `
        CREATE (c:Category {
          id: $id,
          name: $name,
          country: $country,
          summary: $summary,
          refinedSummary: $refinedSummary,
          nodeType: $nodeType,
          level: $level,
          path: $path,
          created_at: $timestamp
        })
      `;
      
      const params = {
        id: node.id,
        name: node.name,
        country: node.country,
        summary: node.summary,
        refinedSummary: node.refinedSummary,
        nodeType: node.nodeType,
        level: node.level,
        path: node.path,
        timestamp: new Date().toISOString()
      };

      await executeQuery(graphName, query, params);
    }
    
    console.log(`Created ${categoryNodes.length} category nodes`);
  } catch (error) {
    console.error('Error creating category nodes:', error);
    throw error;
  }
}

/**
 * Create record nodes in FalkorDB
 */
export async function createRecordNodes(
  graphName: string,
  recordNodes: RecordNode[]
): Promise<void> {
  try {
    for (const node of recordNodes) {
      const query = `
        CREATE (r:Record {
          id: $id,
          recordId: $recordId,
          title: $title,
          htmlUrl: $htmlUrl,
          breadcrumb: $breadcrumb,
          isDeepest: $isDeepest,
          country: $country,
          sectionId: $sectionId,
          cleanedBody: $cleanedBody,
          nodeType: $nodeType,
          created_at: $timestamp
        })
      `;
      
      const params = {
        id: node.id,
        recordId: node.recordId,
        title: node.title,
        htmlUrl: node.htmlUrl,
        breadcrumb: node.breadcrumb,
        isDeepest: node.isDeepest,
        country: node.country,
        sectionId: node.sectionId,
        cleanedBody: node.cleanedBody,
        nodeType: node.nodeType,
        timestamp: new Date().toISOString()
      };
      
      await executeQuery(graphName, query, params);
    }
    
    console.log(`Created ${recordNodes.length} record nodes`);
  } catch (error) {
    console.error('Error creating record nodes:', error);
    throw error;
  }
}

/**
 * Create relationships in FalkorDB
 */
export async function createRelationships(
  graphName: string,
  relationships: Array<{ from: string; to: string; type: string }>
): Promise<void> {
  try {
    for (const rel of relationships) {
      const query = `
        MATCH (from), (to)
        WHERE from.id = $fromId AND to.id = $toId
        CREATE (from)-[:${rel.type} {created_at: $timestamp}]->(to)
      `;
      
      const params = {
        fromId: rel.from,
        toId: rel.to,
        timestamp: new Date().toISOString()
      };
      
      await executeQuery(graphName, query, params);
    }
    
    console.log(`Created ${relationships.length} relationships`);
  } catch (error) {
    console.error('Error creating relationships:', error);
    throw error;
  }
}

/**
 * Upsert a single category node
 */
export async function upsertCategoryNode(
  graphName: string,
  node: CategoryNode
): Promise<void> {
  try {
    const query = `
      MERGE (c:Category {id: $id})
      ON CREATE SET c.created_at = $timestamp
      SET c.name = $name,
          c.country = $country,
          c.summary = $summary,
          c.refinedSummary = $refinedSummary,
          c.nodeType = $nodeType,
          c.level = $level,
          c.path = $path,
          c.updated_at = $timestamp
    `;
    
    const params = {
      id: node.id,
      name: node.name,
      country: node.country,
      summary: node.summary,
      refinedSummary: node.refinedSummary,
      nodeType: node.nodeType,
      level: node.level,
      path: node.path,
      timestamp: new Date().toISOString()
    };
    
    await executeQuery(graphName, query, params);
    console.log(`Upserted category node: ${node.name}`);
  } catch (error) {
    console.error('Error upserting category node:', error);
    throw error;
  }
}

/**
 * Upsert a single record node
 */
export async function upsertRecordNode(
  graphName: string,
  node: RecordNode
): Promise<void> {
  try {
    const query = `
      MERGE (r:Record {id: $id})
      ON CREATE SET r.created_at = $timestamp
      SET r.recordId = $recordId,
          r.title = $title,
          r.htmlUrl = $htmlUrl,
          r.breadcrumb = $breadcrumb,
          r.isDeepest = $isDeepest,
          r.country = $country,
          r.sectionId = $sectionId,
          r.cleanedBody = $cleanedBody,
          r.nodeType = $nodeType,
          r.updated_at = $timestamp
    `;

    const params = {
      id: node.id,
      recordId: node.recordId,
      title: node.title,
      htmlUrl: node.htmlUrl,
      breadcrumb: node.breadcrumb,
      isDeepest: node.isDeepest,
      country: node.country,
      sectionId: node.sectionId,
      cleanedBody: node.cleanedBody,
      nodeType: node.nodeType,
      timestamp: new Date().toISOString()
    };

    await executeQuery(graphName, query, params);
    console.log(`Upserted record node: ${node.title}`);
  } catch (error) {
    console.error('Error upserting record node:', error);
    throw error;
  }
}

/**
 * Retrieve all data from FalkorDB and transform to API format
 */
export async function retrieveDataFromFalkorDB(graphName: string): Promise<{ nodes: Node[]; records: Record[] }> {
  try {
    // Query to get all category nodes
    const categoryQuery = `
      MATCH (c:Category)
      RETURN c
      ORDER BY c.level, c.path
    `;

    // Query to get all records with their category relationships
    const recordQuery = `
      MATCH (r:Record)
      OPTIONAL MATCH (c:Category)-[:CONTAINS]->(r)
      RETURN r, c
      ORDER BY r.title
    `;

    const categoryRawResult = await executeQuery(graphName, categoryQuery);
    const categoryResult = categoryRawResult?.data || [];

    const recordRawResult = await executeQuery(graphName, recordQuery);
    const recordResult = recordRawResult?.data || [];

    // Handle empty graph case
    if ((!categoryResult || categoryResult.length === 0) && (!recordResult || recordResult.length === 0)) {
      console.warn(`Graph '${graphName}' appears to be empty or does not exist`);
      return { nodes: [], records: [] };
    }

    // Transform FalkorDB results to API format
    const { nodes, records } = transformFalkorResultsToAPI(categoryResult, recordResult);

    console.log(`Retrieved ${nodes.length} nodes and ${records.length} records from graph '${graphName}'`);
    return { nodes, records };
  } catch (error) {
    console.error('Error retrieving data from FalkorDB:', error);
    throw error;
  }
}

/**
 * Transform FalkorDB query results to the expected API format
 */
function transformFalkorResultsToAPI(
  categoryResult: any,
  recordResult: any
): { nodes: Node[]; records: Record[] } {
  const nodes: Node[] = [];
  const records: Record[] = [];
  const categoryMap = new Map<string, any>();

  // Process category results
  if (categoryResult && Array.isArray(categoryResult) && categoryResult.length > 0) {
    for (const row of categoryResult) {
      if (row && row.c) {
        const categoryNode = row.c;
        // Extract properties from the FalkorDB node structure
        const category = categoryNode.properties || categoryNode;
        const descendants = row.descendants || [];
        const categoryRecords = row.records || [];

        categoryMap.set(category.id, {
          category,
          descendants,
          records: categoryRecords
        });
      }
    }
  }

  // Process record results and create Record objects
  if (recordResult && Array.isArray(recordResult) && recordResult.length > 0) {
    for (const row of recordResult) {
      if (row && row.r) {
        const recordNode = row.r;
        const categoryNode = row.c;

        // Extract properties from the FalkorDB node structure
        const record = recordNode.properties || recordNode;
        const category = categoryNode ? (categoryNode.properties || categoryNode) : null;

        const apiRecord: Record = {
          id: record.id || `record_${record.recordId}`,
          fields: {
            ID: record.recordId || 0,
            "HTML URL": record.htmlUrl || "",
            Title: record.title || "Untitled Document",
            Breadcrumb: record.breadcrumb || "",
            "Is Deepest": record.isDeepest ? "true" : "false",
            Country: record.country || "general",
            "Section ID": record.sectionId || 0,
            "Cleaned Body": record.cleanedBody || "No content available"
          },
          _bm42_score: Math.random() * 10, // Generate random score for demo
          _search_method: Math.random() > 0.7 ? "bm42" : Math.random() > 0.5 ? "text-search" : "urls",
          _node_path: category ? category.path : ""
        };

        records.push(apiRecord);
      }
    }
  }

  // Build hierarchical node structure
  const rootCategories = Array.from(categoryMap.values())
    .filter(item => item.category && item.category.level === 0);

  for (const rootItem of rootCategories) {
    const node = buildNodeHierarchy(rootItem.category, categoryMap, records);
    nodes.push(node);
  }

  return { nodes, records };
}

/**
 * Recursively build node hierarchy from FalkorDB data
 */
function buildNodeHierarchy(
  category: any,
  categoryMap: Map<string, any>,
  allRecords: Record[]
): Node {
  // Find direct children
  const children: Node[] = [];
  const directChildCategories = Array.from(categoryMap.values())
    .filter(item => {
      // Find categories that are direct children (level = parent.level + 1 and path starts with parent.path)
      return item.category.level === category.level + 1 &&
             item.category.path.startsWith(category.path + '/');
    });

  for (const childItem of directChildCategories) {
    const childNode = buildNodeHierarchy(childItem.category, categoryMap, allRecords);
    children.push(childNode);
  }

  // Find records that belong to this category
  const categoryRecords = allRecords.filter(record => record._node_path === category.path);

  // Calculate total record count (this category + all descendants)
  const totalRecordCount = calculateTotalRecordCount(category, categoryMap, allRecords);

  const node: Node = {
    id: category.id,
    path: category.path,
    country: category.country,
    summary: category.summary || `Documentation for ${category.name}`,
    refined_summary: category.refinedSummary || category.summary || `Comprehensive documentation and guides for ${category.name}`,
    record_count: totalRecordCount,
    has_children: children.length > 0,
    children: children,
    has_records: categoryRecords.length > 0
  };

  return node;
}

/**
 * Calculate total record count for a category including all descendants
 */
function calculateTotalRecordCount(
  category: any,
  categoryMap: Map<string, any>,
  allRecords: Record[]
): number {
  // Count records directly in this category
  const directRecords = allRecords.filter(record => record._node_path === category.path);
  let totalCount = directRecords.length;

  // Count records in all descendant categories
  const descendantCategories = Array.from(categoryMap.values())
    .filter(item => item.category.path.startsWith(category.path + '/'));

  for (const descendant of descendantCategories) {
    const descendantRecords = allRecords.filter(record => record._node_path === descendant.category.path);
    totalCount += descendantRecords.length;
  }

  return totalCount;
}
