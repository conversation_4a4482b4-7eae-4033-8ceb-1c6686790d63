import { NextRequest, NextResponse } from 'next/server';
import { getEvaluationService } from '@/lib/evaluation-utils';
import type { EvaluationRequest } from '@/lib/evaluation-service';
import { recordEvaluation, recordError } from './metrics/route';

export async function POST(request: NextRequest) {
  const startTime = Date.now();
  
  try {
    const body = await request.json();
    
    // Handle batch evaluation
    if (body.batch && Array.isArray(body.evaluations)) {
      return await handleBatchEvaluation(body.evaluations, startTime);
    }
    
    // Handle single evaluation
    return await handleSingleEvaluation(body, startTime);

  } catch (error) {
    console.error('Evaluation API error:', error);
    recordError();
    
    return NextResponse.json(
      { 
        error: 'Evaluation failed',
        details: error instanceof Error ? error.message : 'Unknown error occurred',
        duration: Date.now() - startTime
      },
      { status: 500 }
    );
  }
}

async function handleSingleEvaluation(body: any, startTime: number) {
  // Validate request body
  if (!body.question || !body.answer || !body.context) {
    return NextResponse.json(
      { 
        error: 'Missing required fields: question, answer, and context are required',
        details: {
          hasQuestion: !!body.question,
          hasAnswer: !!body.answer,
          hasContext: !!body.context
        }
      },
      { status: 400 }
    );
  }

  const evaluationRequest: EvaluationRequest = {
    question: body.question,
    answer: body.answer,
    context: body.context
  };

  const evaluationService = getEvaluationService();
  const result = await evaluationService.evaluateGroundedness(evaluationRequest);

  const duration = Date.now() - startTime;
  recordEvaluation(duration, result.overall_score, false);

  return NextResponse.json({
    success: true,
    evaluation: result,
    duration
  });
}

async function handleBatchEvaluation(evaluations: any[], startTime: number) {
  const evaluationService = getEvaluationService();
  const results: any[] = [];
  const errors: any[] = [];

  // Process evaluations concurrently with limit
  const concurrencyLimit = 3; // Limit concurrent requests to avoid overwhelming the LLM API
  const chunks = [];
  
  for (let i = 0; i < evaluations.length; i += concurrencyLimit) {
    chunks.push(evaluations.slice(i, i + concurrencyLimit));
  }

  for (const chunk of chunks) {
    const chunkPromises = chunk.map(async (evalRequest: any, index: number) => {
      try {
        // Validate each request
        if (!evalRequest.question || !evalRequest.answer || !evalRequest.context) {
          throw new Error(`Invalid evaluation request at index ${index}: missing required fields`);
        }

        const request: EvaluationRequest = {
          question: evalRequest.question,
          answer: evalRequest.answer,
          context: evalRequest.context
        };

        const evaluation = await evaluationService.evaluateGroundedness(request);
        return { 
          index: evalRequest.index || index, 
          success: true, 
          evaluation 
        };
      } catch (error) {
        console.error(`Batch evaluation error at index ${index}:`, error);
        return { 
          index: evalRequest.index || index, 
          success: false, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        };
      }
    });

    const chunkResults = await Promise.all(chunkPromises);
    
    chunkResults.forEach(result => {
      if (result.success) {
        results.push(result);
      } else {
        errors.push(result);
      }
    });
  }

  const duration = Date.now() - startTime;
  
  // Record batch evaluation metrics
  if (results.length > 0) {
    const averageScore = results.reduce((sum, r) => sum + (r.evaluation?.overall_score || 0), 0) / results.length;
    recordEvaluation(duration, averageScore, true);
  }

  return NextResponse.json({
    success: true,
    batch: true,
    total: evaluations.length,
    successful: results.length,
    failed: errors.length,
    results,
    errors: errors.length > 0 ? errors : undefined,
    duration
  });
}

export async function GET() {
  try {
    const evaluationService = getEvaluationService();
    const healthCheck = await evaluationService.healthCheck();
    
    return NextResponse.json({
      service: 'evaluation-api',
      status: healthCheck.status,
      timestamp: healthCheck.timestamp,
      version: '1.0.0'
    });
    
  } catch (error) {
    console.error('Health check failed:', error);
    
    return NextResponse.json(
      {
        service: 'evaluation-api',
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 503 }
    );
  }
} 