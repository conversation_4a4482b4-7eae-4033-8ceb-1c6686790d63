import { NextRequest, NextResponse } from 'next/server';
import { getEvaluationService } from '@/lib/evaluation-utils';

export async function GET(request: NextRequest) {
  const startTime = Date.now();
  
  try {
    const evaluationService = getEvaluationService();
    
    // Perform a quick health check
    const healthStatus = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      responseTime: 0,
      services: {
        evaluationService: 'operational',
        geminiApi: 'unknown'
      }
    };

    // Test Gemini API connection
    try {
      // Quick test with minimal data
      const testResult = await evaluationService.evaluateGroundedness({
        question: 'Test question',
        answer: 'Test answer',
        context: 'Test context'
      });
      
      healthStatus.services.geminiApi = testResult ? 'operational' : 'degraded';
    } catch (error) {
      console.error('Health check - Gemini API error:', error);
      healthStatus.services.geminiApi = 'unavailable';
      healthStatus.status = 'degraded';
    }

    healthStatus.responseTime = Date.now() - startTime;

    return NextResponse.json(healthStatus, {
      status: healthStatus.status === 'healthy' ? 200 : 503
    });

  } catch (error) {
    console.error('Health check error:', error);
    
    return NextResponse.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error',
      responseTime: Date.now() - startTime
    }, { status: 503 });
  }
} 