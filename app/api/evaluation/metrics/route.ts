import { NextRequest, NextResponse } from 'next/server';
import { getEvaluationService } from '@/lib/evaluation-utils';

// Simple in-memory metrics store (in production, use Redis or similar)
const metrics = {
  totalEvaluations: 0,
  totalBatchEvaluations: 0,
  averageResponseTime: 0,
  lastEvaluationTime: null as Date | null,
  errorCount: 0,
  responseTimes: [] as number[],
  scoreDistribution: {
    excellent: 0, // 90-100
    good: 0,      // 70-89
    fair: 0,      // 40-69
    poor: 0,      // 1-39
    unsupported: 0 // 0-1
  }
};

export function recordEvaluation(responseTime: number, overallScore: number, isBatch: boolean = false) {
  metrics.totalEvaluations++;
  if (isBatch) metrics.totalBatchEvaluations++;
  
  metrics.lastEvaluationTime = new Date();
  metrics.responseTimes.push(responseTime);
  
  // Keep only last 100 response times for average calculation
  if (metrics.responseTimes.length > 100) {
    metrics.responseTimes = metrics.responseTimes.slice(-100);
  }
  
  metrics.averageResponseTime = metrics.responseTimes.reduce((a, b) => a + b, 0) / metrics.responseTimes.length;
  
  // Record score distribution
  if (overallScore >= 90) metrics.scoreDistribution.excellent++;
  else if (overallScore >= 70) metrics.scoreDistribution.good++;
  else if (overallScore >= 40) metrics.scoreDistribution.fair++;
  else if (overallScore >= 1) metrics.scoreDistribution.poor++;
  else metrics.scoreDistribution.unsupported++;
}

export function recordError() {
  metrics.errorCount++;
}

export async function GET(request: NextRequest) {
  try {
    const currentMetrics = {
      ...metrics,
      uptime: process.uptime(),
      timestamp: new Date().toISOString(),
      successRate: metrics.totalEvaluations > 0 
        ? ((metrics.totalEvaluations - metrics.errorCount) / metrics.totalEvaluations * 100).toFixed(2) + '%'
        : '0%',
      evaluationsPerMinute: metrics.totalEvaluations > 0 && metrics.lastEvaluationTime
        ? (metrics.totalEvaluations / (process.uptime() / 60)).toFixed(2)
        : '0',
      medianResponseTime: calculateMedian(metrics.responseTimes),
      p95ResponseTime: calculatePercentile(metrics.responseTimes, 95),
      memoryUsage: process.memoryUsage()
    };

    return NextResponse.json(currentMetrics);

  } catch (error) {
    console.error('Metrics endpoint error:', error);
    
    return NextResponse.json({
      error: 'Failed to retrieve metrics',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

function calculateMedian(numbers: number[]): number {
  if (numbers.length === 0) return 0;
  
  const sorted = [...numbers].sort((a, b) => a - b);
  const middle = Math.floor(sorted.length / 2);
  
  if (sorted.length % 2 === 0) {
    return (sorted[middle - 1] + sorted[middle]) / 2;
  }
  
  return sorted[middle];
}

function calculatePercentile(numbers: number[], percentile: number): number {
  if (numbers.length === 0) return 0;
  
  const sorted = [...numbers].sort((a, b) => a - b);
  const index = Math.ceil(sorted.length * (percentile / 100)) - 1;
  
  return sorted[Math.max(0, index)];
} 