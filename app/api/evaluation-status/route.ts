import { NextRequest, NextResponse } from 'next/server';

// In a real implementation, you'd store evaluation results in a database
// For demo purposes, we'll use in-memory storage
const evaluationCache = new Map<string, any>();

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const requestId = searchParams.get('id');

    if (!requestId) {
      return NextResponse.json(
        { error: 'Request ID is required' },
        { status: 400 }
      );
    }

    const evaluation = evaluationCache.get(requestId);
    
    if (evaluation) {
      return NextResponse.json({
        status: 'completed',
        evaluation
      });
    } else {
      return NextResponse.json({
        status: 'processing'
      });
    }

  } catch (error) {
    console.error('Evaluation status error:', error);
    return NextResponse.json(
      { error: 'Failed to check evaluation status' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { requestId, evaluation } = await request.json();

    if (!requestId || !evaluation) {
      return NextResponse.json(
        { error: 'Request ID and evaluation data are required' },
        { status: 400 }
      );
    }

    // Store evaluation result
    evaluationCache.set(requestId, evaluation);

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Store evaluation error:', error);
    return NextResponse.json(
      { error: 'Failed to store evaluation' },
      { status: 500 }
    );
  }
} 