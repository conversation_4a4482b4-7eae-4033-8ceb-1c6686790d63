import { NextRequest, NextResponse } from 'next/server';

// In a real implementation, you'd store evaluation results in a database
// For demo purposes, we'll use in-memory storage
const evaluationCache = new Map<string, any>();

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const requestId = searchParams.get('id');

    if (!requestId) {
      return NextResponse.json(
        { error: 'Request ID is required' },
        { status: 400 }
      );
    }

    const evaluation = evaluationCache.get(requestId);

    console.log('🔍 Checking evaluation status for:', requestId);
    console.log('📊 Cache contains:', Array.from(evaluationCache.keys()));
    console.log('✅ Found evaluation:', !!evaluation);

    if (evaluation) {
      console.log('📤 Returning completed evaluation with', evaluation.claim_analysis?.length || 0, 'claims');
      return NextResponse.json({
        status: 'completed',
        evaluation
      });
    } else {
      console.log('⏳ Evaluation still processing for:', requestId);
      return NextResponse.json({
        status: 'processing'
      });
    }

  } catch (error) {
    console.error('❌ Evaluation status error:', error);
    return NextResponse.json(
      { error: 'Failed to check evaluation status' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { requestId, evaluation } = body;

    console.log('📥 Received evaluation storage request:', {
      requestId,
      hasEvaluation: !!evaluation,
      evaluationKeys: evaluation ? Object.keys(evaluation) : [],
      claimCount: evaluation?.claim_analysis?.length || 0
    });

    if (!requestId || !evaluation) {
      console.error('❌ Missing required fields:', { requestId: !!requestId, evaluation: !!evaluation });
      return NextResponse.json(
        { error: 'Request ID and evaluation data are required' },
        { status: 400 }
      );
    }

    // Store evaluation result
    evaluationCache.set(requestId, evaluation);
    console.log('✅ Successfully stored evaluation for:', requestId);
    console.log('📊 Cache now contains:', evaluationCache.size, 'evaluations');

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('❌ Store evaluation error:', error);
    return NextResponse.json(
      { error: 'Failed to store evaluation' },
      { status: 500 }
    );
  }
}