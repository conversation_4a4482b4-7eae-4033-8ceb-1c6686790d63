import { NextResponse } from 'next/server'
import { retrieveDataFromFalkorDB } from '@/lib/data-transformer'
import { healthCheck } from '@/lib/falkordb'

// Default graph name for the knowledge base
const DEFAULT_GRAPH_NAME = 'knowledge_graph'

export async function GET(request: Request) {
  try {
    // Parse URL to get optional graph name parameter
    const url = new URL(request.url)
    const graphName = url.searchParams.get('graph') || DEFAULT_GRAPH_NAME
    
    // Optional health check parameter
    const checkHealth = url.searchParams.get('health') === 'true'
    
    if (checkHealth) {
      // Perform health check first
      const health = await healthCheck()
      if (health.status !== 'healthy') {
        return NextResponse.json(
          { 
            success: false, 
            error: 'FalkorDB connection unhealthy',
            details: health.message
          },
          { status: 503 }
        )
      }
    }
    
    // Retrieve data from FalkorDB
    const { nodes, records } = await retrieveDataFromFalkorDB(graphName)
    
    // Return in the same format as the original API
    return NextResponse.json({
      success: true,
      data: {
        nodes,
        records
      },
      meta: {
        source: 'falkordb',
        graph: graphName,
        timestamp: new Date().toISOString(),
        node_count: nodes.length,
        record_count: records.length
      }
    })
    
  } catch (error) {
    console.error('Error in FalkorDB data API:', error)
    
    // Determine error type and status code
    let statusCode = 500
    let errorMessage = 'Failed to load data from FalkorDB'
    
    if (error instanceof Error) {
      if (error.message.includes('Graph') && error.message.includes('does not exist')) {
        statusCode = 404
        errorMessage = 'Knowledge graph not found'
      } else if (error.message.includes('connection')) {
        statusCode = 503
        errorMessage = 'Database connection failed'
      } else {
        errorMessage = error.message
      }
    }
    
    return NextResponse.json(
      { 
        success: false, 
        error: errorMessage,
        details: error instanceof Error ? error.message : 'Unknown error occurred',
        source: 'falkordb'
      },
      { status: statusCode }
    )
  }
}

// Optional POST endpoint for testing or administrative operations
export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { action, graphName = DEFAULT_GRAPH_NAME } = body
    
    if (action === 'health-check') {
      const health = await healthCheck()
      return NextResponse.json({
        success: true,
        health,
        timestamp: new Date().toISOString()
      })
    }
    
    if (action === 'reload-data') {
      // Reload data from the specified graph
      const { nodes, records } = await retrieveDataFromFalkorDB(graphName)
      
      return NextResponse.json({
        success: true,
        message: 'Data reloaded successfully',
        data: {
          nodes,
          records
        },
        meta: {
          source: 'falkordb',
          graph: graphName,
          timestamp: new Date().toISOString(),
          node_count: nodes.length,
          record_count: records.length
        }
      })
    }
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Invalid action. Supported actions: health-check, reload-data' 
      },
      { status: 400 }
    )
    
  } catch (error) {
    console.error('Error in FalkorDB data API POST:', error)
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to process request',
        details: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    )
  }
}
