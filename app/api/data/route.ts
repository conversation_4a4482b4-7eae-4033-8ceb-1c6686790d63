import { NextResponse } from 'next/server'
import type { Node, Record } from '@/types'
import { retrieveDataFromFalkorDB } from '@/lib/data-transformer'
import { healthCheck } from '@/lib/falkordb'
import fs from 'fs'
import path from 'path'

interface JsonNode {
  children: { [key: string]: JsonNode }
  records: Array<{
    id: string
    fields: {
      ID: number
      "HTML URL": string
      Title?: string
      "Cleaned Body": string
      Breadcrumb: string
      "Is Deepest": string
      Country: string
      "Section ID": number
    }
  }>
  id: string
  country: string
  summary?: string
  refinedSummary?: string
}

interface JsonData {
  children: { [key: string]: JsonNode }
}

function transformJsonToNodes(jsonData: JsonData, basePath = ""): { nodes: Node[]; records: Record[] } {
  const nodes: Node[] = []
  const allRecords: Record[] = []

  for (const [nodeTitle, nodeData] of Object.entries(jsonData.children)) {
    const currentPath = basePath ? `${basePath}/${nodeTitle}` : nodeTitle

    // Transform records for this node
    const nodeRecords: Record[] = nodeData.records.map((record) => ({
      ...record,
      _bm42_score: Math.random() * 10, // Generate random score for demo
      _search_method: Math.random() > 0.7 ? "bm42" : Math.random() > 0.5 ? "text-search" : ("urls" as const),
      _node_path: currentPath,
      fields: {
        ...record.fields,
        Title: record.fields.Title || "Untitled Document",
        "Cleaned Body": record.fields["Cleaned Body"] || "No content available",
      },
    }))

    // Recursively process children
    const childResult = transformJsonToNodes({ children: nodeData.children }, currentPath)
    const childNodes = childResult.nodes
    const childRecords = childResult.records

    // Calculate total record count (this node + all descendants)
    const totalRecordCount = nodeRecords.length + childRecords.length

    // Create the node
    const node: Node = {
      id: nodeData.id,
      path: currentPath,
      country: nodeData.country,
      summary: nodeData.summary || `Documentation for ${nodeTitle}`,
      refined_summary:
        nodeData.refinedSummary || nodeData.summary || `Comprehensive documentation and guides for ${nodeTitle}`,
      record_count: totalRecordCount,
      has_children: Object.keys(nodeData.children).length > 0,
      children: childNodes,
      has_records: nodeRecords.length > 0,
    }

    nodes.push(node)
    allRecords.push(...nodeRecords, ...childRecords)
  }

  return { nodes, records: allRecords }
}

// Default graph name for the knowledge base
const DEFAULT_GRAPH_NAME = 'knowledge_graph'

export async function GET(request: Request) {
  try {
    // Parse URL to get optional parameters
    const url = new URL(request.url)
    const useFalkorDB = url.searchParams.get('source') === 'falkordb'
    const graphName = url.searchParams.get('graph') || DEFAULT_GRAPH_NAME

    let nodes: Node[] = []
    let records: Record[] = []
    let source = 'static'

    if (useFalkorDB) {
      try {
        // Try to get data from FalkorDB first
        const falkorData = await retrieveDataFromFalkorDB(graphName)
        nodes = falkorData.nodes
        records = falkorData.records
        source = 'falkordb'

        console.log(`Successfully loaded data from FalkorDB: ${nodes.length} nodes, ${records.length} records`)
      } catch (falkorError) {
        console.warn('FalkorDB failed, falling back to static data:', falkorError)

        // Fallback to static data if FalkorDB fails
        const filePath = path.join(process.cwd(), 'mockdata.json')
        const fileContents = fs.readFileSync(filePath, 'utf8')
        const mockDataJson = JSON.parse(fileContents)

        const staticData = transformJsonToNodes(mockDataJson as JsonData)
        nodes = staticData.nodes
        records = staticData.records
        source = 'static-fallback'
      }
    } else {
      // Explicitly use static data
      const filePath = path.join(process.cwd(), 'mockdata.json')
      const fileContents = fs.readFileSync(filePath, 'utf8')
      const mockDataJson = JSON.parse(fileContents)

      const staticData = transformJsonToNodes(mockDataJson as JsonData)
      nodes = staticData.nodes
      records = staticData.records
      source = 'static'
    }

    return NextResponse.json({
      success: true,
      data: {
        nodes,
        records
      }
    })
  } catch (error) {
    console.error('Error loading data:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to load data',
        details: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    )
  }
}