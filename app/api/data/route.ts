import { NextResponse } from 'next/server'
import type { Node, Record } from '@/types'
import fs from 'fs'
import path from 'path'

interface JsonNode {
  children: { [key: string]: JsonNode }
  records: Array<{
    id: string
    fields: {
      ID: number
      "HTML URL": string
      Title?: string
      "Cleaned Body": string
      Breadcrumb: string
      "Is Deepest": string
      Country: string
      "Section ID": number
    }
  }>
  id: string
  country: string
  summary?: string
  refinedSummary?: string
}

interface JsonData {
  children: { [key: string]: JsonNode }
}

function transformJsonToNodes(jsonData: JsonData, basePath = ""): { nodes: Node[]; records: Record[] } {
  const nodes: Node[] = []
  const allRecords: Record[] = []

  for (const [nodeTitle, nodeData] of Object.entries(jsonData.children)) {
    const currentPath = basePath ? `${basePath}/${nodeTitle}` : nodeTitle

    // Transform records for this node
    const nodeRecords: Record[] = nodeData.records.map((record) => ({
      ...record,
      _bm42_score: Math.random() * 10, // Generate random score for demo
      _search_method: Math.random() > 0.7 ? "bm42" : Math.random() > 0.5 ? "text-search" : ("urls" as const),
      _node_path: currentPath,
      fields: {
        ...record.fields,
        Title: record.fields.Title || "Untitled Document",
        "Cleaned Body": record.fields["Cleaned Body"] || "No content available",
      },
    }))

    // Recursively process children
    const childResult = transformJsonToNodes({ children: nodeData.children }, currentPath)
    const childNodes = childResult.nodes
    const childRecords = childResult.records

    // Calculate total record count (this node + all descendants)
    const totalRecordCount = nodeRecords.length + childRecords.length

    // Create the node
    const node: Node = {
      id: nodeData.id,
      path: currentPath,
      country: nodeData.country,
      summary: nodeData.summary || `Documentation for ${nodeTitle}`,
      refined_summary:
        nodeData.refinedSummary || nodeData.summary || `Comprehensive documentation and guides for ${nodeTitle}`,
      record_count: totalRecordCount,
      has_children: Object.keys(nodeData.children).length > 0,
      children: childNodes,
      has_records: nodeRecords.length > 0,
    }

    nodes.push(node)
    allRecords.push(...nodeRecords, ...childRecords)
  }

  return { nodes, records: allRecords }
}

export async function GET() {
  try {
    // Read the mock data JSON file
    const filePath = path.join(process.cwd(), 'mockdata.json')
    const fileContents = fs.readFileSync(filePath, 'utf8')
    const mockDataJson = JSON.parse(fileContents)
    
    // Transform the JSON data to the expected format
    const { nodes, records } = transformJsonToNodes(mockDataJson as JsonData)
    
    return NextResponse.json({
      success: true,
      data: {
        nodes,
        records
      }
    })
  } catch (error) {
    console.error('Error loading mock data:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to load data' 
      },
      { status: 500 }
    )
  }
} 