import { GoogleGenAI } from '@google/genai';
import { NextRequest, NextResponse } from 'next/server';
import { getEvaluationService } from '@/lib/evaluation-utils';
import type { EvaluationRequest } from '@/lib/evaluation-service';

// Initialize the GoogleGenAI client
const genAI = new GoogleGenAI({
  apiKey: process.env.GEMINI_API_KEY!,
});

export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const { query, selectedRecords, includeEvaluation = false } = await request.json();

    if (!query || !selectedRecords || selectedRecords.length === 0) {
      return NextResponse.json(
        { error: 'Query and selected records are required' },
        { status: 400 }
      );
    }

    // Format the context from selected records
    const context = selectedRecords.map((record: any, index: number) => {
      return `Record ${index + 1}:
Title: ${record.fields.Title || `Record #${record.fields.ID}`}
Content: ${record.fields["Cleaned Body"]}
Source: ${record.fields["HTML URL"]}
Country: ${record.fields.Country}
Section: ${record.fields["Section ID"]}
Breadcrumb: ${record.fields.Breadcrumb}
---`;
    }).join('\n\n');

    // Create the prompt for Gemini
    const prompt = `You are an AI assistant helping to analyze and answer questions about knowledge base records. 

Context Records:
${context}

User Query: ${query}

Please provide a comprehensive answer based on the provided records. If you reference specific information, mention which record(s) it comes from. Be accurate and cite your sources from the provided context.`;

    // Generate content using Gemini 2.0 Flash
    const response = await genAI.models.generateContent({
      model: 'gemini-2.0-flash',
      contents: prompt,
      config: {
        temperature: 0.3,
        maxOutputTokens: 2048,
      },
    });

    // Extract the text response
    const aiResponse = response.text || '';

    const responseData: any = {
      response: aiResponse,
      recordsUsed: selectedRecords.length,
    };

    // Optional evaluation integration
    if (includeEvaluation) {
      // Option A: Fast response - return immediately and evaluate in background
      if (process.env.ENABLE_BACKGROUND_EVAL === 'true') {
        // Generate a unique request ID for this evaluation
        const requestId = `eval_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        responseData.requestId = requestId;
        
        // Start evaluation in background (fire-and-forget)
        setImmediate(async () => {
          try {
            const evaluationService = getEvaluationService();
            const evaluationRequest: EvaluationRequest = {
              question: query,
              answer: aiResponse,
              context: context
            };
            const evaluation = await evaluationService.evaluateGroundedness(evaluationRequest);
            
            // Store the evaluation result
            const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
            console.log(`Storing evaluation result for ${requestId} at ${baseUrl}/api/evaluation-status`);

            const storeResponse = await fetch(`${baseUrl}/api/evaluation-status`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ requestId, evaluation })
            });

            if (!storeResponse.ok) {
              console.error('Failed to store evaluation result:', await storeResponse.text());
            } else {
              console.log('Successfully stored evaluation result for:', requestId);
            }
            
            console.log('Background evaluation completed for request:', requestId);
          } catch (error) {
            console.error('Background evaluation failed:', error);
          }
        });
        
        responseData.evaluationStatus = 'processing';
        
      } else {
        // Option B: Current behavior - wait for evaluation
        try {
          const evaluationService = getEvaluationService();
          const evaluationRequest: EvaluationRequest = {
            question: query,
            answer: aiResponse,
            context: context
          };

          const evaluation = await evaluationService.evaluateGroundedness(evaluationRequest);
          responseData.evaluation = evaluation;
          responseData.evaluationTimestamp = new Date().toISOString();

        } catch (evaluationError) {
          console.error('Evaluation failed during Gemini API call:', evaluationError);
          responseData.evaluationError = evaluationError instanceof Error 
            ? evaluationError.message 
            : 'Evaluation service unavailable';
        }
      }
    }

    return NextResponse.json(responseData);

  } catch (error) {
    console.error('Gemini API error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to generate response',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
} 