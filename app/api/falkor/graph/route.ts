import { NextRequest, NextResponse } from 'next/server';
import { 
  createGraph, 
  graphExists, 
  deleteGraph, 
  getGraphStats,
  healthCheck 
} from '@/lib/falkordb';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { graphName, overwrite = false } = body;

    // Validate input
    if (!graphName || typeof graphName !== 'string') {
      return NextResponse.json(
        { error: 'Graph name is required and must be a string' },
        { status: 400 }
      );
    }

    // Validate graph name format (alphanumeric and underscores only)
    if (!/^[a-zA-Z0-9_]+$/.test(graphName)) {
      return NextResponse.json(
        { error: 'Graph name must contain only alphanumeric characters and underscores' },
        { status: 400 }
      );
    }

    // Check if graph already exists
    const exists = await graphExists(graphName);
    
    if (exists && !overwrite) {
      return NextResponse.json(
        { 
          error: 'Graph already exists',
          message: `Graph '${graphName}' already exists. Set overwrite=true to replace it.`
        },
        { status: 409 }
      );
    }

    // Delete existing graph if overwrite is true
    if (exists && overwrite) {
      await deleteGraph(graphName);
    }

    // Create new graph
    await createGraph(graphName);

    return NextResponse.json({
      success: true,
      message: `Graph '${graphName}' created successfully`,
      graphName,
      overwritten: exists && overwrite
    });

  } catch (error) {
    console.error('Error in graph creation API:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const graphName = searchParams.get('name');
    const action = searchParams.get('action');

    // Health check endpoint
    if (action === 'health') {
      const health = await healthCheck();
      return NextResponse.json(health);
    }

    // Get graph statistics
    if (action === 'stats' && graphName) {
      const exists = await graphExists(graphName);
      
      if (!exists) {
        return NextResponse.json(
          { error: `Graph '${graphName}' does not exist` },
          { status: 404 }
        );
      }

      const stats = await getGraphStats(graphName);
      return NextResponse.json({
        success: true,
        stats
      });
    }

    // Check if graph exists
    if (action === 'exists' && graphName) {
      const exists = await graphExists(graphName);
      return NextResponse.json({
        success: true,
        exists,
        graphName
      });
    }

    // Default: return usage information
    return NextResponse.json({
      message: 'FalkorDB Graph API',
      endpoints: {
        'POST /api/falkor/graph': 'Create a new graph',
        'GET /api/falkor/graph?action=health': 'Check FalkorDB health',
        'GET /api/falkor/graph?action=exists&name=<graphName>': 'Check if graph exists',
        'GET /api/falkor/graph?action=stats&name=<graphName>': 'Get graph statistics',
        'DELETE /api/falkor/graph?name=<graphName>': 'Delete a graph'
      },
      examples: {
        createGraph: {
          method: 'POST',
          body: {
            graphName: 'my_knowledge_graph',
            overwrite: false
          }
        }
      }
    });

  } catch (error) {
    console.error('Error in graph GET API:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const graphName = searchParams.get('name');

    // Validate input
    if (!graphName) {
      return NextResponse.json(
        { error: 'Graph name is required as a query parameter' },
        { status: 400 }
      );
    }

    // Check if graph exists
    const exists = await graphExists(graphName);
    
    if (!exists) {
      return NextResponse.json(
        { error: `Graph '${graphName}' does not exist` },
        { status: 404 }
      );
    }

    // Delete the graph
    await deleteGraph(graphName);

    return NextResponse.json({
      success: true,
      message: `Graph '${graphName}' deleted successfully`,
      graphName
    });

  } catch (error) {
    console.error('Error in graph deletion API:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}
