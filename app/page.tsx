"use client"

import { useState, useEffect } from "react"
import { TreeView } from "@/components/tree-view"
import { SearchView } from "@/components/search-view"
import { Header } from "@/components/header"
import type { Node, Record, SearchState } from "@/types"
import { fetchData } from "@/lib/api"
import { ChatInput } from "@/components/chat-input"
import ResponseModal from "@/components/response-modal"

type View = "tree" | "search"

export default function ChromeSidebarApp() {
  const [currentView, setCurrentView] = useState<View>("tree")
  const [searchState, setSearchState] = useState<SearchState>({
    query: "",
    mode: "records",
    filters: {},
    results: [],
    isLoading: false,
  })
  const [nodes, setNodes] = useState<Node[]>([])
  const [records, setRecords] = useState<Record[]>([])
  const [highlightedNodeId, setHighlightedNodeId] = useState<string | null>(null)
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set())
  const [scrollToNodeId, setScrollToNodeId] = useState<string | null>(null)
  const [selectedRecord, setSelectedRecord] = useState<Record | null>(null)
  const [selectedRecords, setSelectedRecords] = useState<Set<string>>(new Set())
  const [isAiResponseOpen, setIsAiResponseOpen] = useState(false)

  useEffect(() => {
    // Load data from API
    const loadData = async () => {
      try {
        const { nodes, records } = await fetchData()
        setNodes(nodes)
        setRecords(records)
      } catch (error) {
        console.error("Failed to load data:", error)
        setNodes([])
        setRecords([])
      }
    }

    loadData()
  }, [])

  // Separate effect for cleanup on unmount
  useEffect(() => {
    return () => {
      if (currentPollingCleanup) {
        currentPollingCleanup();
      }
    }
  }, [])

  const findNodeByPath = (nodes: Node[], targetPath: string): Node | null => {
    for (const node of nodes) {
      if (node.path === targetPath) {
        return node
      }
      if (node.children.length > 0) {
        const found = findNodeByPath(node.children, targetPath)
        if (found) return found
      }
    }
    return null
  }

  const findAllParentPaths = (nodes: Node[], targetPath: string, currentPath = ""): string[] => {
    const parentPaths: string[] = []

    for (const node of nodes) {
      const nodePath = currentPath ? `${currentPath}/${node.path.split("/").pop()}` : node.path

      if (targetPath.startsWith(nodePath) && targetPath !== nodePath) {
        parentPaths.push(node.id)

        if (node.children.length > 0) {
          const childPaths = findAllParentPaths(node.children, targetPath, nodePath)
          parentPaths.push(...childPaths)
        }
      }
    }

    return parentPaths
  }

  const expandPathToNode = (targetPath: string) => {
    // Find all parent node IDs that need to be expanded
    const parentIds = findAllParentPaths(nodes, targetPath)

    // Add them to expanded nodes
    setExpandedNodes((prev) => {
      const newExpanded = new Set(prev)
      parentIds.forEach((id) => newExpanded.add(id))
      return newExpanded
    })
  }

  const handleNodeSelect = (node: Node) => {
    // Switch to tree view
    setCurrentView("tree")

    // Expand all parent nodes to make the target visible
    expandPathToNode(node.path)

    // Set scroll target
    setScrollToNodeId(node.id)

    // Highlight the node with animation
    setHighlightedNodeId(node.id)

    // Clear highlight after animation
    setTimeout(() => {
      setHighlightedNodeId(null)
    }, 2000)

    console.log("Node selected:", node.path)
  }

  const handleHierarchyNodeSelect = (nodePath: string) => {
    const targetNode = findNodeByPath(nodes, nodePath)
    if (targetNode) {
      handleNodeSelect(targetNode)
    }
  }

  const handleRecordSelect = (record: Record) => {
    setSelectedRecord(record)
    // Note: Record selection now happens within ResponseModal's unified interface
    // This is kept for compatibility with other components like TreeView and SearchView
  }

  const handleRecordToggle = (recordId: string) => {
    setSelectedRecords((prev) => {
      const newSelected = new Set(prev)
      if (newSelected.has(recordId)) {
        newSelected.delete(recordId)
      } else {
        newSelected.add(recordId)
      }
      return newSelected
    })
  }

  const handleClearSelectedRecords = () => {
    setSelectedRecords(new Set())
  }

  const [llmResponse, setLlmResponse] = useState<{
    response: string;
    evals: {
      claim_analysis: any[];
    };
  } | null>(null);
  const [isResponseLoading, setIsResponseLoading] = useState(false);
  const [responseError, setResponseError] = useState<string | null>(null);
  const [isEvaluationLoading, setIsEvaluationLoading] = useState(false);
  const [currentPollingCleanup, setCurrentPollingCleanup] = useState<(() => void) | null>(null);

  // Polling function for background evaluation results
  const pollForEvaluationResults = (requestId: string) => {
    const maxAttempts = 20; // Poll for up to 40 seconds (20 * 2s intervals)
    let attempts = 0;
    let isPolling = true;

    const poll = async () => {
      if (!isPolling) return; // Stop if polling was cancelled

      try {
        const response = await fetch(`/api/evaluation-status?id=${requestId}`);
        const data = await response.json();

        if (data.status === 'completed' && data.evaluation) {
          // Update the response with evaluation data
          setLlmResponse(prev => prev ? {
            ...prev,
            evals: {
              claim_analysis: data.evaluation.claim_analysis || []
            }
          } : null);
          setIsEvaluationLoading(false);
          isPolling = false; // Stop polling
          console.log(`Evaluation polling completed for request: ${requestId}`);
          return;
        }

        // Continue polling if not completed and haven't exceeded max attempts
        attempts++;
        if (attempts < maxAttempts && isPolling) {
          setTimeout(poll, 2000); // Poll every 2 seconds
        } else {
          console.warn(`Evaluation polling timed out for request: ${requestId}`);
          setIsEvaluationLoading(false);
          isPolling = false;
        }
      } catch (error) {
        console.error('Error polling for evaluation results:', error);
        setIsEvaluationLoading(false);
        isPolling = false;
      }
    };

    // Start polling after a short delay
    setTimeout(poll, 1000);
    
    // Return a cleanup function
    return () => {
      isPolling = false;
    };
  };

  const handleSubmitQuery = async (query: string) => {
    // Clean up any existing polling
    if (currentPollingCleanup) {
      currentPollingCleanup();
      setCurrentPollingCleanup(null);
    }

    // Show loading state and open modal
    setIsResponseLoading(true);
    setResponseError(null);
    setLlmResponse(null);
    setIsEvaluationLoading(false);
    setIsAiResponseOpen(true);

    try {
      // Get the selected records data
      const selectedRecordsData = Array.from(selectedRecords)
        .map((id) => records.find((r) => r.id === id)!)
        .filter(Boolean);

      // Call the Gemini API with evaluation enabled
      const response = await fetch('/api/gemini', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query,
          selectedRecords: selectedRecordsData,
          includeEvaluation: true, // Enable evaluation
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to get AI response');
      }

      // Handle different response types
      if (data.evaluationStatus === 'processing' && data.requestId) {
        // Background evaluation mode - poll for results
        const transformedResponse = {
          response: data.response,
          evals: {
            claim_analysis: []
          }
        };
        setLlmResponse(transformedResponse);
        setIsEvaluationLoading(true);
        
        // Start polling for evaluation results and store cleanup function
        const cleanup = pollForEvaluationResults(data.requestId);
        setCurrentPollingCleanup(cleanup);
        
      } else {
        // Standard mode or evaluation completed
        const transformedResponse = {
          response: data.response,
          evals: {
            claim_analysis: data.evaluation?.claim_analysis || []
          }
        };
        setLlmResponse(transformedResponse);

        // If evaluation failed, show a warning
        if (data.evaluationError) {
          console.warn('Evaluation failed:', data.evaluationError);
        }
      }

    } catch (error) {
      console.error('Failed to get AI response:', error);
      setResponseError(error instanceof Error ? error.message : 'Unknown error occurred');
    } finally {
      setIsResponseLoading(false);
    }
  }

  const handleSearch = async (query: string, mode: SearchState["mode"], filters: SearchState["filters"]) => {
    // Prevent duplicate searches
    if (searchState.query === query && searchState.mode === mode && searchState.isLoading) {
      return
    }

    setSearchState((prev) => ({
      ...prev,
      isLoading: true,
      query,
      mode,
      filters,
      // Clear results when mode changes or new search
      results: [],
    }))

    // Simulate AI search
    setTimeout(() => {
      let results: (Node | Record)[] = []

      if (mode === "nodes") {
        // NODES SEARCH - Only return Node objects
        const searchTerms = query
          .toLowerCase()
          .split(" ")
          .filter((term) => term.length > 0)

        const searchAllNodes = (nodeList: Node[]): Node[] => {
          const matchedNodes: Node[] = []

          for (const node of nodeList) {
            // Check if node matches search terms
            const nodePath = node.path.toLowerCase()
            const nodeSummary = (node.refined_summary || node.summary).toLowerCase()
            const nodeTitle = node.path.split("/").pop()?.toLowerCase() || ""

            const pathMatches = searchTerms.some(
              (term) => nodePath.includes(term) || nodeSummary.includes(term) || nodeTitle.includes(term),
            )

            if (pathMatches) {
              matchedNodes.push({
                ...node,
                _search_score: calculateNodeScore(node, searchTerms),
              } as Node & { _search_score: number })
            }

            // Recursively search children
            if (node.children.length > 0) {
              const childMatches = searchAllNodes(node.children)
              matchedNodes.push(...childMatches)
            }
          }

          return matchedNodes
        }

        const calculateNodeScore = (node: Node, searchTerms: string[]): number => {
          let score = 0
          const nodePath = node.path.toLowerCase()
          const nodeSummary = (node.refined_summary || node.summary).toLowerCase()
          const nodeTitle = node.path.split("/").pop()?.toLowerCase() || ""

          // Higher score for title matches
          searchTerms.forEach((term) => {
            if (nodeTitle.includes(term)) score += 10
            if (nodePath.includes(term)) score += 5
            if (nodeSummary.includes(term)) score += 2
          })

          // Bonus for exact matches
          if (searchTerms.some((term) => nodeTitle === term)) score += 20

          return score
        }

        // Only return Node objects for nodes search
        results = searchAllNodes(nodes)
          .sort((a, b) => ((b as any)._search_score || 0) - ((a as any)._search_score || 0))
          .slice(0, 20) // Limit results
      } else if (mode === "records") {
        // RECORDS SEARCH - Only return Record objects
        results = records
          .filter(
            (record) =>
              record.fields["Cleaned Body"].toLowerCase().includes(query.toLowerCase()) ||
              record.fields.Breadcrumb.toLowerCase().includes(query.toLowerCase()) ||
              record.fields.Title.toLowerCase().includes(query.toLowerCase()),
          )
          .map((record) => ({
            ...record,
            _bm42_score: Math.random() * 10,
          }))
          .sort((a, b) => (b as Record)._bm42_score - (a as Record)._bm42_score)
          .slice(0, 50) // Limit results
      } else if (mode === "urls") {
        // URLs SEARCH - Return records but focus on URL matching
        results = records
          .filter((record) => record.fields["HTML URL"].toLowerCase().includes(query.toLowerCase()))
          .map((record) => ({
            ...record,
            _bm42_score: Math.random() * 10,
          }))
          .sort((a, b) => (b as Record)._bm42_score - (a as Record)._bm42_score)
          .slice(0, 50)
      }

      console.log(`Search mode: ${mode}, Results count: ${results.length}`, results)

      // Only update if the query hasn't changed during the search
      setSearchState((prev) => {
        if (prev.query === query && prev.mode === mode) {
          return {
            ...prev,
            results,
            isLoading: false,
          }
        }
        return prev
      })
    }, 800)
  }

  const getViewTitle = () => {
    switch (currentView) {
      case "tree":
        return "Knowledge Base"
      case "search":
        return "Search"
      default:
        return "Knowledge Base"
    }
  }

  const canGoBack = currentView !== "tree"

  const handleBack = () => {
    if (currentView === "search") {
      setCurrentView("tree")
    }
  }

  return (
    <div className="h-screen w-full flex flex-col bg-background">
      <Header
        title={getViewTitle()}
        canGoBack={canGoBack}
        onBack={handleBack}
        onSearchToggle={() => setCurrentView(currentView === "search" ? "tree" : "search")}
        isSearchActive={currentView === "search"}
      />

      <div className="flex-1 overflow-hidden">
        {currentView === "tree" && (
          <TreeView
            nodes={nodes}
            records={records}
            onNodeSelect={handleNodeSelect}
            onRecordSelect={handleRecordSelect}
            highlightedNodeId={highlightedNodeId}
            expandedNodes={expandedNodes}
            onExpandedNodesChange={setExpandedNodes}
            scrollToNodeId={scrollToNodeId}
            onScrollComplete={() => setScrollToNodeId(null)}
            selectedRecords={selectedRecords}
            onRecordToggle={handleRecordToggle}
          />
        )}

        {currentView === "search" && (
          <SearchView
            searchState={searchState}
            onSearch={handleSearch}
            onNodeSelect={handleNodeSelect}
            onHierarchyNodeSelect={handleHierarchyNodeSelect}
            onResultSelect={(result) => {
              if ("path" in result) {
                handleNodeSelect(result as Node)
              } else {
                handleRecordSelect(result as Record)
              }
            }}
          />
        )}
      </div>

      {/* Chat Input - only show when records are selected */}
      {selectedRecords.size > 0 && (
        <ChatInput
          selectedRecords={Array.from(selectedRecords)
            .map((id) => records.find((r) => r.id === id)!)
            .filter(Boolean)}
          onClearSelection={handleClearSelectedRecords}
          onSubmit={handleSubmitQuery}
          isLoading={isResponseLoading}
        />
      )}

      {/* Response Modal with integrated Record viewing */}
      <ResponseModal 
        response={llmResponse} 
        selectedRecords={Array.from(selectedRecords)
          .map((id) => records.find((r) => r.id === id)!)
          .filter(Boolean)}
        isOpen={isAiResponseOpen} 
        onClose={() => {
          // Clean up polling when modal is closed
          if (currentPollingCleanup) {
            currentPollingCleanup();
            setCurrentPollingCleanup(null);
          }
          setIsAiResponseOpen(false);
          setIsEvaluationLoading(false);
        }} 
        isEvaluationLoading={isEvaluationLoading}
      />

      {/* Loading Modal */}
      {isResponseLoading && isAiResponseOpen && (
        <div className="fixed inset-0 z-60 flex items-center justify-center">
          <div className="absolute inset-0 bg-black/30 backdrop-blur-sm" />
          <div className="relative bg-white rounded-xl p-8 shadow-2xl max-w-md mx-4">
            <div className="text-center">
              <div className="w-8 h-8 border-2 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Generating Response</h3>
              <p className="text-sm text-gray-600">AI is analyzing your records and evaluating the response...</p>
            </div>
          </div>
        </div>
      )}

      {/* Error Display */}
      {responseError && isAiResponseOpen && (
        <div className="fixed bottom-4 left-4 right-4 bg-red-50 border border-red-200 rounded-lg p-4 z-50">
          <div className="flex items-center justify-between">
            <p className="text-red-800 text-sm">{responseError}</p>
            <button 
              onClick={() => setResponseError(null)} 
              className="text-red-600 hover:text-red-800"
            >
              ×
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
