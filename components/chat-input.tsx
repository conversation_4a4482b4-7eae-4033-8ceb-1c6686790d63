"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent } from "@/components/ui/card"
import { X, Send, FileText, ExternalLink, Sparkles } from "lucide-react"
import type { Record } from "@/types"

interface ChatInputProps {
  selectedRecords: Record[]
  onClearSelection: () => void
  onSubmit: (query: string) => void
  isLoading?: boolean
}

export function ChatInput({ selectedRecords, onClearSelection, onSubmit, isLoading }: ChatInputProps) {
  const [query, setQuery] = useState("")
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  // Auto-resize textarea with larger min/max heights
  useEffect(() => {
    const textarea = textareaRef.current
    if (textarea) {
      textarea.style.height = "auto"
      textarea.style.height = `${Math.min(Math.max(textarea.scrollHeight, 80), 200)}px`
    }
  }, [query])

  const handleSubmit = () => {
    if (query.trim()) {
      onSubmit(query.trim())
      setQuery("")
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSubmit()
    }
  }

  const removeRecord = (recordId: string) => {
    // This will be handled by the parent component
    // For now, we'll just call onClearSelection if it's the last record
    if (selectedRecords.length === 1) {
      onClearSelection()
    }
  }

  const getBM42ScoreColor = (score: number) => {
    if (score >= 8) return "bg-green-500"
    if (score >= 6) return "bg-yellow-500"
    return "bg-red-500"
  }

  const truncateContent = (content: string, maxLength: number) => {
    if (content.length <= maxLength) return content
    return content.substring(0, maxLength) + "..."
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 z-40">
      {/* Main container with light purple background */}
      <div className="bg-purple-50/80 dark:bg-purple-950/30 backdrop-blur-sm border-t border-purple-200/50 dark:border-purple-700/50 shadow-2xl rounded-t-2xl">
        {/* Subtle top border accent */}
        <div className="h-0.5 bg-gradient-to-r from-transparent via-purple-400/40 to-transparent"></div>

        <div className="max-w-4xl mx-auto p-6">
          {/* Header with selected count */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <Sparkles className="h-4 w-4 text-purple-600 dark:text-purple-400" />
              <span className="text-sm font-medium text-purple-800 dark:text-purple-200">
                {selectedRecords.length} record{selectedRecords.length > 1 ? "s" : ""} selected
              </span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClearSelection}
              className="text-xs text-purple-600 hover:text-purple-800 dark:text-purple-400 dark:hover:text-purple-200 h-6 hover:bg-purple-100 dark:hover:bg-purple-900/50"
            >
              Clear all
            </Button>
          </div>

          {/* Selected Records Display - Enhanced Cards */}
          <div className="mb-5 space-y-2 max-h-32 overflow-y-auto scrollbar-thin scrollbar-thumb-purple-300 dark:scrollbar-thumb-purple-600">
            {selectedRecords.map((record, index) => {
              const title = record.fields.Title || `Record #${record.fields.ID}`
              const content = record.fields["Cleaned Body"]
              // Calculate available space for content (rough estimate)
              const titleLength = title.length
              const availableSpace = Math.max(60 - titleLength, 20)
              const truncatedContent = truncateContent(content, availableSpace)

              return (
                <Card
                  key={record.id}
                  className="bg-gradient-to-r from-purple-100 to-purple-100/70 border-purple-300/60 dark:from-purple-900/60 dark:to-purple-900/30 dark:border-purple-700/60 shadow-sm hover:shadow-md transition-all duration-200 group"
                >
                  <CardContent className="p-3">
                    <div className="flex items-center gap-3">
                      {/* Record number badge */}
                      <div className="flex items-center gap-2">
                        <div className="w-6 h-6 rounded-full bg-purple-200 dark:bg-purple-800/70 flex items-center justify-center">
                          <span className="text-xs font-medium text-purple-800 dark:text-purple-200">{index + 1}</span>
                        </div>
                        <FileText className="h-3.5 w-3.5 text-purple-700 dark:text-purple-300" />
                      </div>

                      <div className="flex-1 min-w-0 flex items-center gap-3">
                        <span className="font-medium text-sm text-purple-900 dark:text-purple-100 truncate">
                          {title}
                        </span>
                        <span className="text-xs text-purple-700/70 dark:text-purple-300/70 truncate">
                          {truncatedContent}
                        </span>
                      </div>

                      <div className="flex items-center gap-2 shrink-0 opacity-80 group-hover:opacity-100 transition-opacity">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0 hover:bg-purple-200/80 dark:hover:bg-purple-700/60 rounded-full"
                          onClick={() => window.open(record.fields["HTML URL"], "_blank")}
                        >
                          <ExternalLink className="h-3 w-3" />
                        </Button>

                        <div
                          className={`px-2 py-0.5 rounded-full text-white text-xs font-medium min-w-[28px] text-center shadow-sm ${getBM42ScoreColor(record._bm42_score)}`}
                        >
                          {record._bm42_score.toFixed(1)}
                        </div>

                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0 hover:bg-red-100 hover:text-red-600 dark:hover:bg-red-900/30 dark:hover:text-red-400 rounded-full transition-colors"
                          onClick={() => removeRecord(record.id)}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>

          {/* Input Area - White textarea with enhanced design */}
          <div className="flex items-end gap-4">
            <div className="flex-1 relative">
              <Textarea
                ref={textareaRef}
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder={`Ask a question about your selected record${selectedRecords.length > 1 ? "s" : ""}...`}
                className="min-h-[80px] max-h-[200px] resize-none pr-16 bg-white dark:bg-gray-800 text-base leading-relaxed border-purple-200 dark:border-purple-700 shadow-sm focus:shadow-md focus:border-purple-300 dark:focus:border-purple-600 transition-all duration-200 rounded-xl"
                rows={3}
              />
              <div className="absolute bottom-3 right-3 flex items-center gap-2">
                <div className="text-xs text-purple-500 bg-purple-100 dark:bg-purple-800 dark:text-purple-300 px-2 py-1 rounded-full">
                  {selectedRecords.length}
                </div>
              </div>
            </div>

            <div className="flex flex-col gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={onClearSelection}
                className="h-10 px-4 border-purple-200 dark:border-purple-700 hover:bg-purple-100 dark:hover:bg-purple-900/50 text-purple-700 dark:text-purple-300"
              >
                Clear
              </Button>
              <Button
                onClick={handleSubmit}
                disabled={!query.trim() || isLoading}
                className="h-10 px-6 bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <>
                    <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    Thinking...
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" />
                    Send
                  </>
                )}
              </Button>
            </div>
          </div>

          {/* Enhanced Helper text */}
          <div className="mt-3 flex items-center justify-center gap-4 text-xs text-purple-600/70 dark:text-purple-400/70">
            <span className="flex items-center gap-1">
              <kbd className="px-1.5 py-0.5 bg-purple-200/60 dark:bg-purple-800/60 text-purple-800 dark:text-purple-200 rounded text-xs">
                Enter
              </kbd>
              to send
            </span>
            <span className="flex items-center gap-1">
              <kbd className="px-1.5 py-0.5 bg-purple-200/60 dark:bg-purple-800/60 text-purple-800 dark:text-purple-200 rounded text-xs">
                Shift
              </kbd>
              +
              <kbd className="px-1.5 py-0.5 bg-purple-200/60 dark:bg-purple-800/60 text-purple-800 dark:text-purple-200 rounded text-xs">
                Enter
              </kbd>
              for new line
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}
