"use client"

import { useState, useMemo } from "react"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ChevronRight, ChevronDown, Search, ExpandIcon, ShrinkIcon } from "lucide-react"
import type { Node } from "@/types"

interface TreePanelProps {
  nodes: Node[]
  selectedNode: Node | null
  onNodeSelect: (node: Node) => void
}

export function TreePanel({ nodes, selectedNode, onNodeSelect }: TreePanelProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set())

  const filteredNodes = useMemo(() => {
    if (!searchQuery) return nodes
    return nodes.filter(
      (node) =>
        node.path.toLowerCase().includes(searchQuery.toLowerCase()) ||
        node.summary.toLowerCase().includes(searchQuery.toLowerCase()),
    )
  }, [nodes, searchQuery])

  const toggleExpanded = (nodeId: string) => {
    const newExpanded = new Set(expandedNodes)
    if (newExpanded.has(nodeId)) {
      newExpanded.delete(nodeId)
    } else {
      newExpanded.add(nodeId)
    }
    setExpandedNodes(newExpanded)
  }

  const expandAll = () => {
    setExpandedNodes(new Set(nodes.map((n) => n.id)))
  }

  const collapseAll = () => {
    setExpandedNodes(new Set())
  }

  const renderNode = (node: Node, level = 0) => {
    const isExpanded = expandedNodes.has(node.id)
    const isSelected = selectedNode?.id === node.id

    return (
      <div key={node.id}>
        <div
          className={`flex items-center gap-2 p-2 hover:bg-accent cursor-pointer ${
            isSelected ? "bg-accent border-l-2 border-primary" : ""
          }`}
          style={{ paddingLeft: `${level * 16 + 8}px` }}
          onClick={() => onNodeSelect(node)}
        >
          {node.has_children && (
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
              onClick={(e) => {
                e.stopPropagation()
                toggleExpanded(node.id)
              }}
            >
              {isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
            </Button>
          )}

          <div className="flex-1 min-w-0">
            <div className="font-medium text-sm truncate">{node.path.split("/").pop()}</div>
            <div className="text-xs text-muted-foreground truncate">{node.country}</div>
          </div>

          <Badge variant="secondary" className="text-xs">
            {node.record_count}
          </Badge>
        </div>

        {isExpanded && node.children.map((child) => renderNode(child, level + 1))}
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      <div className="p-4 border-b border-border">
        <div className="relative mb-3">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Filter nodes..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={expandAll}>
            <ExpandIcon className="h-4 w-4 mr-1" />
            Expand All
          </Button>
          <Button variant="outline" size="sm" onClick={collapseAll}>
            <ShrinkIcon className="h-4 w-4 mr-1" />
            Collapse All
          </Button>
        </div>
      </div>

      <div className="flex-1 overflow-auto">
        {filteredNodes.length === 0 ? (
          <div className="p-4 text-center text-muted-foreground">No nodes found</div>
        ) : (
          filteredNodes.map((node) => renderNode(node))
        )}
      </div>

      <div className="p-4 border-t border-border text-sm text-muted-foreground">
        {filteredNodes.length} of {nodes.length} nodes
      </div>
    </div>
  )
}
