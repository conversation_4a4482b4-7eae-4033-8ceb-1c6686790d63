"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowLeft, Search, X } from "lucide-react"

interface HeaderProps {
  title: string
  canGoBack: boolean
  onBack: () => void
  onSearchToggle: () => void
  isSearchActive: boolean
}

export function Header({ title, canGoBack, onBack, onSearchToggle, isSearchActive }: HeaderProps) {
  return (
    <header className="h-16 border-b border-border bg-background px-4 flex items-center justify-between shrink-0">
      <div className="flex items-center gap-3 min-w-0">
        {canGoBack && (
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={onBack}>
            <ArrowLeft className="h-5 w-5" />
          </Button>
        )}
        <h1 className="font-semibold text-xl truncate">{title}</h1>
      </div>

      <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={onSearchToggle}>
        {isSearchActive ? <X className="h-5 w-5" /> : <Search className="h-5 w-5" />}
      </Button>
    </header>
  )
}
