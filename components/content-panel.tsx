"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { ExternalLink, ChevronRight } from "lucide-react"
import type { Node, Record } from "@/types"

interface ContentPanelProps {
  selectedNode: Node | null
  selectedRecord: Record | null
  records: Record[]
  onRecordSelect: (record: Record) => void
  onNodeSelect: (node: Node) => void
}

export function ContentPanel({
  selectedNode,
  selectedRecord,
  records,
  onRecordSelect,
  onNodeSelect,
}: ContentPanelProps) {
  const [currentPage, setCurrentPage] = useState(1)
  const recordsPerPage = 50

  const nodeRecords = selectedNode ? records.filter((r) => r._node_path === selectedNode.path) : []

  const paginatedRecords = nodeRecords.slice((currentPage - 1) * recordsPerPage, currentPage * recordsPerPage)

  const getBM42ScoreColor = (score: number) => {
    if (score >= 8) return "bg-green-500"
    if (score >= 6) return "bg-yellow-500"
    return "bg-red-500"
  }

  const getMethodBadgeVariant = (method: string) => {
    switch (method) {
      case "bm42":
        return "default"
      case "text-search":
        return "secondary"
      case "urls":
        return "outline"
      default:
        return "secondary"
    }
  }

  if (selectedRecord) {
    return (
      <div className="h-full overflow-auto p-6">
        <div className="max-w-4xl mx-auto">
          <div className="mb-4">
            <Button variant="ghost" onClick={() => onRecordSelect(null as any)} className="mb-2">
              ← Back to list
            </Button>
            <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
              <span>{selectedRecord.fields.Breadcrumb}</span>
            </div>
          </div>

          <Card>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    Record #{selectedRecord.fields.ID}
                    <a
                      href={selectedRecord.fields["HTML URL"]}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary hover:text-primary/80"
                    >
                      <ExternalLink className="h-4 w-4" />
                    </a>
                  </CardTitle>
                  <div className="flex items-center gap-2 mt-2">
                    <Badge variant="secondary">{selectedRecord.fields.Country}</Badge>
                    <Badge variant="outline">Section {selectedRecord.fields["Section ID"]}</Badge>
                    {selectedRecord.fields["Is Deepest"] === "Yes" && <Badge variant="default">Deepest</Badge>}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant={getMethodBadgeVariant(selectedRecord._search_method)}>
                    {selectedRecord._search_method}
                  </Badge>
                  <div
                    className={`px-2 py-1 rounded text-white text-xs font-medium ${getBM42ScoreColor(selectedRecord._bm42_score)}`}
                  >
                    {selectedRecord._bm42_score.toFixed(1)}
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="prose max-w-none">
                <p className="text-sm leading-relaxed">{selectedRecord.fields["Cleaned Body"]}</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  if (selectedNode) {
    return (
      <div className="h-full overflow-auto">
        <Tabs defaultValue="details" className="h-full flex flex-col">
          <div className="border-b border-border">
            <TabsList className="ml-6 mt-4">
              <TabsTrigger value="details">Node Details</TabsTrigger>
              {selectedNode.has_records && <TabsTrigger value="records">Records ({nodeRecords.length})</TabsTrigger>}
            </TabsList>
          </div>

          <TabsContent value="details" className="flex-1 p-6">
            <div className="max-w-4xl mx-auto">
              <div className="mb-6">
                <div className="text-sm text-muted-foreground mb-2">{selectedNode.path}</div>
                <h1 className="text-3xl font-bold mb-2">{selectedNode.path.split("/").pop()}</h1>
                <Badge variant="secondary">{selectedNode.country}</Badge>
              </div>

              <Card className="mb-6">
                <CardHeader>
                  <CardTitle>Description</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm leading-relaxed">{selectedNode.refined_summary || selectedNode.summary}</p>
                </CardContent>
              </Card>

              {selectedNode.children.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>Child Nodes</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {selectedNode.children.map((child) => (
                        <div
                          key={child.id}
                          className="flex items-center justify-between p-3 border border-border rounded-lg hover:bg-accent cursor-pointer"
                          onClick={() => onNodeSelect(child)}
                        >
                          <div>
                            <div className="font-medium">{child.path.split("/").pop()}</div>
                            <div className="text-sm text-muted-foreground">{child.summary}</div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant="secondary">{child.record_count}</Badge>
                            <ChevronRight className="h-4 w-4" />
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>

          {selectedNode.has_records && (
            <TabsContent value="records" className="flex-1 overflow-auto p-6">
              <div className="space-y-4">
                {paginatedRecords.map((record) => (
                  <Card
                    key={record.id}
                    className="cursor-pointer hover:shadow-md transition-shadow"
                    onClick={() => onRecordSelect(record)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">#{record.fields.ID}</span>
                          <a
                            href={record.fields["HTML URL"]}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-primary hover:text-primary/80"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <ExternalLink className="h-4 w-4" />
                          </a>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant={getMethodBadgeVariant(record._search_method)}>{record._search_method}</Badge>
                          <div
                            className={`px-2 py-1 rounded text-white text-xs font-medium ${getBM42ScoreColor(record._bm42_score)}`}
                          >
                            {record._bm42_score.toFixed(1)}
                          </div>
                        </div>
                      </div>

                      <div className="text-sm text-muted-foreground mb-2">{record.fields.Breadcrumb}</div>

                      <p className="text-sm line-clamp-3">{record.fields["Cleaned Body"]}</p>

                      <div className="flex items-center gap-2 mt-3">
                        <Badge variant="secondary">{record.fields.Country}</Badge>
                        <Badge variant="outline">Section {record.fields["Section ID"]}</Badge>
                        {record.fields["Is Deepest"] === "Yes" && <Badge variant="default">Deepest</Badge>}
                      </div>
                    </CardContent>
                  </Card>
                ))}

                {nodeRecords.length > recordsPerPage && (
                  <div className="flex justify-center gap-2 mt-6">
                    <Button
                      variant="outline"
                      disabled={currentPage === 1}
                      onClick={() => setCurrentPage(currentPage - 1)}
                    >
                      Previous
                    </Button>
                    <span className="flex items-center px-4">
                      Page {currentPage} of {Math.ceil(nodeRecords.length / recordsPerPage)}
                    </span>
                    <Button
                      variant="outline"
                      disabled={currentPage >= Math.ceil(nodeRecords.length / recordsPerPage)}
                      onClick={() => setCurrentPage(currentPage + 1)}
                    >
                      Next
                    </Button>
                  </div>
                )}
              </div>
            </TabsContent>
          )}
        </Tabs>
      </div>
    )
  }

  return (
    <div className="h-full flex items-center justify-center text-muted-foreground">
      <div className="text-center">
        <h2 className="text-xl font-semibold mb-2">Welcome to Tree Viewer</h2>
        <p>Select a node from the tree to view its details and records</p>
      </div>
    </div>
  )
}
