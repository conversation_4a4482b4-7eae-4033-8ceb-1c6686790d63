"use client"

import { useState, useEffect } from "react"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Search, ExternalLink, Folder, FileText, ChevronRight } from "lucide-react"
import type { SearchState, Node, Record } from "@/types"

interface SearchViewProps {
  searchState: SearchState
  onSearch: (query: string, mode: SearchState["mode"], filters: SearchState["filters"]) => void
  onNodeSelect: (node: Node) => void
  onHierarchyNodeSelect: (nodePath: string) => void
  onResultSelect: (result: Node | Record) => void
}

export function SearchView({
  searchState,
  onSearch,
  onNodeSelect,
  onHierarchyNodeSelect,
  onResultSelect,
}: SearchViewProps) {
  const [query, setQuery] = useState("")
  const [mode, setMode] = useState<SearchState["mode"]>("records")

  useEffect(() => {
    // Only update local state if it's different from searchState
    if (searchState.query !== query) {
      setQuery(searchState.query)
    }
    if (searchState.mode !== mode) {
      setMode(searchState.mode)
    }
  }, [searchState.query, searchState.mode])

  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      if (query.trim() && (query !== searchState.query || mode !== searchState.mode)) {
        onSearch(query, mode, {})
      }
    }, 300)

    return () => clearTimeout(debounceTimer)
  }, [query, mode, onSearch, searchState.query, searchState.mode])

  const getBM42ScoreColor = (score: number) => {
    if (score >= 8) return "bg-green-500"
    if (score >= 6) return "bg-yellow-500"
    return "bg-red-500"
  }

  const getMethodBadgeVariant = (method: string) => {
    switch (method) {
      case "bm42":
        return "default"
      case "text-search":
        return "secondary"
      case "urls":
        return "outline"
      default:
        return "secondary"
    }
  }

  const renderNodeResult = (node: Node) => {
    // Ensure this is actually a node object
    if (!node.path || !node.id) {
      console.error("Invalid node object:", node)
      return null
    }

    const pathSegments = node.path.split("/")
    const matchedNodeTitle = pathSegments[pathSegments.length - 1]
    const parentPath = pathSegments.slice(0, -1)

    return (
      <Card
        key={node.id}
        className="cursor-pointer hover:shadow-md transition-shadow"
        onClick={() => onNodeSelect(node)}
      >
        <CardContent className="p-4">
          {/* Breadcrumb hierarchy at the top - clickable */}
          {parentPath.length > 0 && (
            <div className="flex items-center gap-1 mb-3 text-sm text-muted-foreground">
              {parentPath.map((segment, idx) => {
                const segmentPath = pathSegments.slice(0, idx + 1).join("/")
                return (
                  <div key={idx} className="flex items-center gap-1">
                    <button
                      className="flex items-center gap-1 hover:text-primary transition-colors"
                      onClick={(e) => {
                        e.stopPropagation()
                        onHierarchyNodeSelect(segmentPath)
                      }}
                    >
                      <Folder className="h-4 w-4" />
                      <span className="font-medium">{segment}</span>
                    </button>
                    {idx < parentPath.length - 1 && <ChevronRight className="h-4 w-4" />}
                  </div>
                )
              })}
            </div>
          )}

          {/* Clean nested tree display - clickable */}
          <div className="mb-4 bg-muted/20 rounded-lg p-3">
            <div className="text-xs font-medium text-muted-foreground mb-2">Hierarchy:</div>
            {pathSegments.map((segment, idx) => {
              const isMatched = idx === pathSegments.length - 1
              const depth = idx
              const segmentPath = pathSegments.slice(0, idx + 1).join("/")

              return (
                <button
                  key={idx}
                  className={`w-full flex items-center gap-2 py-1 px-2 rounded transition-colors ${
                    isMatched ? "bg-primary/10 border border-primary/20" : "hover:bg-muted/40"
                  }`}
                  style={{ marginLeft: `${depth * 16}px` }}
                  onClick={(e) => {
                    e.stopPropagation()
                    onHierarchyNodeSelect(segmentPath)
                  }}
                >
                  {/* Indentation indicator */}
                  {depth > 0 && <div className="flex items-center text-muted-foreground/40">{"└".repeat(1)}</div>}

                  <Folder className={`h-4 w-4 ${isMatched ? "text-primary" : "text-muted-foreground"}`} />
                  <span className={`text-sm font-medium ${isMatched ? "text-primary" : "text-muted-foreground"}`}>
                    {segment}
                  </span>

                  {isMatched && (
                    <Badge variant="outline" className="text-xs ml-auto">
                      Matched
                    </Badge>
                  )}
                </button>
              )
            })}
          </div>

          {/* Matched node details */}
          <div className="bg-primary/5 border border-primary/20 rounded-lg p-3 mb-3">
            <div className="flex items-center gap-2 mb-2">
              <Folder className="h-5 w-5 text-primary" />
              <span className="font-semibold text-base text-primary">{matchedNodeTitle}</span>
            </div>

            {/* Node description */}
            <p className="text-sm text-foreground leading-relaxed mb-3">
              {(node.refined_summary || node.summary).substring(0, 200)}
              {(node.refined_summary || node.summary).length > 200 && "..."}
            </p>

            {/* Metadata */}
            <div className="flex items-center gap-2">
              <Badge variant="secondary" className="text-xs">
                {node.country}
              </Badge>
              <Badge variant="outline" className="text-xs">
                {node.record_count} records
              </Badge>
              {node.has_children && (
                <Badge variant="default" className="text-xs">
                  {node.children.length} children
                </Badge>
              )}
            </div>
          </div>

          {/* Full path for reference */}
          <div className="text-xs text-muted-foreground font-mono bg-muted/30 px-2 py-1 rounded">{node.path}</div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* Search Header */}
      <div className="p-3 border-b border-border space-y-3">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search with AI-powered BM42..."
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            className="pl-10 h-9"
            autoFocus
          />
        </div>

        <Tabs value={mode} onValueChange={(value) => setMode(value as SearchState["mode"])}>
          <TabsList className="grid w-full grid-cols-3 h-8">
            <TabsTrigger value="nodes" className="text-xs">
              Nodes
            </TabsTrigger>
            <TabsTrigger value="records" className="text-xs">
              Records
            </TabsTrigger>
            <TabsTrigger value="urls" className="text-xs">
              URLs
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Search Results */}
      <div className="flex-1 overflow-auto p-3">
        {searchState.isLoading ? (
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="h-3 bg-muted rounded w-1/2 mb-2"></div>
                <div className="bg-muted/50 rounded-lg p-3">
                  <div className="h-5 bg-muted rounded w-3/4 mb-2"></div>
                  <div className="h-4 bg-muted rounded w-full mb-1"></div>
                  <div className="h-4 bg-muted rounded w-2/3"></div>
                </div>
              </div>
            ))}
          </div>
        ) : searchState.results.length === 0 && query ? (
          <div className="text-center text-muted-foreground py-8">
            <Search className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p className="text-sm">No {mode} found</p>
            <p className="text-xs">Try different search terms</p>
          </div>
        ) : query ? (
          <div className="space-y-4">
            {searchState.results.map((result) => {
              const isNode = "path" in result
              const node = result as Node
              const record = result as Record

              return isNode ? (
                renderNodeResult(node)
              ) : (
                // Records rendering (only for records mode)
                <Card
                  key={record.id}
                  className="cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() => onResultSelect(result)}
                >
                  <CardContent className="p-3">
                    {/* Header Row */}
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <FileText className="h-4 w-4 text-muted-foreground" />
                        <span className="font-medium text-sm">#{record.fields.ID}</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-5 w-5 p-0"
                          onClick={(e) => {
                            e.stopPropagation()
                            window.open(record.fields["HTML URL"], "_blank")
                          }}
                        >
                          <ExternalLink className="h-3 w-3" />
                        </Button>
                      </div>
                      <div className="flex items-center gap-1">
                        <Badge variant={getMethodBadgeVariant(record._search_method)} className="text-xs h-5 px-1.5">
                          {record._search_method === "bm42"
                            ? "AI"
                            : record._search_method === "text-search"
                              ? "TXT"
                              : "URL"}
                        </Badge>
                        <div
                          className={`px-1.5 py-0.5 rounded text-white text-xs font-medium min-w-[28px] text-center ${getBM42ScoreColor(record._bm42_score)}`}
                        >
                          {record._bm42_score.toFixed(1)}
                        </div>
                      </div>
                    </div>

                    {/* Content Summary */}
                    <div className="mb-2">
                      <p className="text-xs leading-relaxed line-clamp-2">
                        {record.fields["Cleaned Body"].substring(0, 120)}...
                      </p>
                    </div>

                    {/* Metadata Row */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-1">
                        <Badge variant="secondary" className="text-xs h-4 px-1.5">
                          {record.fields.Country}
                        </Badge>
                        <Badge variant="outline" className="text-xs h-4 px-1.5">
                          §{record.fields["Section ID"]}
                        </Badge>
                        {record.fields["Is Deepest"] === "Yes" && (
                          <Badge variant="default" className="text-xs h-4 px-1.5">
                            Deep
                          </Badge>
                        )}
                      </div>
                      <div className="text-xs text-muted-foreground truncate max-w-[100px]">
                        {record.fields.Breadcrumb.split(" > ").pop()}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        ) : (
          <div className="text-center text-muted-foreground py-8">
            <Search className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p className="text-sm">Start typing to search</p>
            <p className="text-xs">AI-powered semantic search</p>
          </div>
        )}

        {searchState.results.length > 0 && (
          <div className="text-center text-xs text-muted-foreground mt-4 pb-4">
            {searchState.results.length} {mode} found
          </div>
        )}
      </div>
    </div>
  )
}
