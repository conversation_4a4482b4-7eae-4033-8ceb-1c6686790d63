"use client"

import type React from "react"

import { useState, useMemo, useEffect, useRef } from "react"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { ChevronRight, ChevronDown, Search, Folder, FileText, ExternalLink } from "lucide-react"
import type { Node, Record } from "@/types"
import { Checkbox } from "@/components/ui/checkbox"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

interface TreeViewProps {
  nodes: Node[]
  records: Record[]
  onNodeSelect: (node: Node) => void
  onRecordSelect: (record: Record) => void
  highlightedNodeId?: string | null
  expandedNodes: Set<string>
  onExpandedNodesChange: (expandedNodes: Set<string>) => void
  scrollToNodeId?: string | null
  onScrollComplete?: () => void
  selectedRecords: Set<string>
  onRecordToggle: (recordId: string) => void
}

export function TreeView({
  nodes,
  records,
  onNodeSelect,
  onRecordSelect,
  highlightedNodeId,
  expandedNodes,
  onExpandedNodesChange,
  scrollToNodeId,
  onScrollComplete,
  selectedRecords,
  onRecordToggle,
}: TreeViewProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const nodeRefs = useRef<Map<string, HTMLDivElement>>(new Map())

  // Handle scrolling to target node
  useEffect(() => {
    if (scrollToNodeId && scrollContainerRef.current) {
      const targetElement = nodeRefs.current.get(scrollToNodeId)
      if (targetElement) {
        // Wait a bit for expansion animations to complete
        setTimeout(() => {
          targetElement.scrollIntoView({
            behavior: "smooth",
            block: "center",
            inline: "nearest",
          })

          // Call completion callback after scroll
          setTimeout(() => {
            onScrollComplete?.()
          }, 500)
        }, 100)
      }
    }
  }, [scrollToNodeId, expandedNodes, onScrollComplete])

  const filteredNodes = useMemo(() => {
    if (!searchQuery) return nodes
    return nodes.filter(
      (node) =>
        node.path.toLowerCase().includes(searchQuery.toLowerCase()) ||
        node.summary.toLowerCase().includes(searchQuery.toLowerCase()),
    )
  }, [nodes, searchQuery])

  const toggleExpanded = (nodeId: string) => {
    const newExpanded = new Set(expandedNodes)
    if (newExpanded.has(nodeId)) {
      newExpanded.delete(nodeId)
    } else {
      newExpanded.add(nodeId)
    }
    onExpandedNodesChange(newExpanded)
  }

  const getNodeRecords = (nodePath: string) => {
    return records.filter((record) => record._node_path === nodePath)
  }

  // Get all records for a node and its descendants
  const getAllNodeRecords = (node: Node): Record[] => {
    const nodeRecords = getNodeRecords(node.path)
    const childRecords = node.children.flatMap((child) => getAllNodeRecords(child))
    return [...nodeRecords, ...childRecords]
  }

  // Check node selection state
  const getNodeSelectionState = (node: Node): "none" | "partial" | "all" => {
    const allRecords = getAllNodeRecords(node)

    if (allRecords.length === 0) return "none"

    const selectedCount = allRecords.filter((record) => selectedRecords.has(record.id)).length

    if (selectedCount === 0) return "none"
    if (selectedCount === allRecords.length) return "all"
    return "partial"
  }

  // Handle node checkbox selection
  const handleNodeSelection = (node: Node, includeChildren: boolean) => {
    console.log(`Selecting node: ${node.path}, includeChildren: ${includeChildren}`)
    const recordsToToggle = includeChildren ? getAllNodeRecords(node) : getNodeRecords(node.path)
    console.log(`Records to toggle: ${recordsToToggle.length}`)

    // Check if all records are selected
    const allSelected = recordsToToggle.every((record) => selectedRecords.has(record.id))

    // Toggle all records
    recordsToToggle.forEach((record) => {
      if (allSelected) {
        // Deselect all
        if (selectedRecords.has(record.id)) {
          onRecordToggle(record.id)
        }
      } else {
        // Select all
        if (!selectedRecords.has(record.id)) {
          onRecordToggle(record.id)
        }
      }
    })
  }

  const getBM42ScoreColor = (score: number) => {
    if (score >= 8) return "bg-green-500"
    if (score >= 6) return "bg-yellow-500"
    return "bg-red-500"
  }

  const renderNodeCheckbox = (node: Node) => {
    const selectionState = getNodeSelectionState(node)
    const nodeRecords = getNodeRecords(node.path)
    const hasChildren = node.children.length > 0
    const hasDirectRecords = nodeRecords.length > 0
    const allRecords = getAllNodeRecords(node)
    const totalRecords = allRecords.length

    console.log(`=== Node: ${node.path} ===`)
    console.log(`hasChildren: ${hasChildren}`)
    console.log(`hasDirectRecords: ${hasDirectRecords}`)
    console.log(`totalRecords: ${totalRecords}`)

    // Don't show checkbox if no records at all
    if (totalRecords === 0) {
      console.log(`No checkbox - no records`)
      return null
    }

    // Show dropdown if node has children (regardless of direct records)
    const shouldShowDropdown = hasChildren && totalRecords > 0

    console.log(`shouldShowDropdown: ${shouldShowDropdown}`)

    if (!shouldShowDropdown) {
      // Simple checkbox
      console.log(`Showing simple checkbox`)
      return (
        <Checkbox
          checked={selectionState === "all"}
          ref={(ref) => {
            if (ref && selectionState === "partial") {
              ref.indeterminate = true
            }
          }}
          onCheckedChange={() => handleNodeSelection(node, true)}
          className="h-5 w-5"
        />
      )
    }

    // Dropdown with checkbox options
    console.log(`Showing dropdown with checkbox options`)
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <div className="cursor-pointer">
            <Checkbox
              checked={selectionState === "all"}
              ref={(ref) => {
                if (ref && selectionState === "partial") {
                  ref.indeterminate = true
                }
              }}
              className="h-5 w-5"
            />
          </div>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-64">
          {hasDirectRecords && (
            <DropdownMenuItem
              onSelect={() => {
                console.log("Dropdown: Selecting node only")
                handleNodeSelection(node, false)
              }}
            >
              <FileText className="h-4 w-4 mr-2" />
              Select this node only ({nodeRecords.length} records)
            </DropdownMenuItem>
          )}
          <DropdownMenuItem
            onSelect={() => {
              console.log("Dropdown: Selecting all sub-nodes")
              handleNodeSelection(node, true)
            }}
          >
            <Folder className="h-4 w-4 mr-2" />
            Select all sub-nodes ({totalRecords} records total)
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    )
  }

  const renderNode = (node: Node, level = 0): React.ReactNode => {
    const isExpanded = expandedNodes.has(node.id)
    const nodeRecords = getNodeRecords(node.path)
    const isHighlighted = highlightedNodeId === node.id
    const selectionState = getNodeSelectionState(node)

    return (
      <div
        key={node.id}
        className="mb-4"
        ref={(el) => {
          if (el) {
            nodeRefs.current.set(node.id, el)
          } else {
            nodeRefs.current.delete(node.id)
          }
        }}
      >
        <Card
          className={`border-border transition-all duration-500 hover:shadow-md ${
            isHighlighted ? "ring-2 ring-primary ring-opacity-50 bg-primary/5 animate-pulse" : ""
          } ${
            selectionState === "all"
              ? "border-primary/50 bg-primary/5"
              : selectionState === "partial"
                ? "border-primary/30 bg-primary/2"
                : ""
          }`}
        >
          <CardContent className="p-4">
            {/* Node Header */}
            <div className="flex items-start gap-3 mb-3">
              {/* Node Selection Checkbox */}
              <div className="pt-0.5" onClick={(e) => e.stopPropagation()}>
                {renderNodeCheckbox(node)}
              </div>

              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 mt-0.5"
                onClick={(e) => {
                  e.stopPropagation()
                  toggleExpanded(node.id)
                }}
              >
                {isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
              </Button>

              <div className="flex items-start gap-3 flex-1 cursor-pointer" onClick={() => toggleExpanded(node.id)}>
                <Folder className="h-6 w-6 text-muted-foreground mt-0.5 shrink-0" />
                <div className="flex-1 min-w-0">
                  <h3 className="font-semibold text-lg text-foreground mb-1" style={{ paddingLeft: `${level * 16}px` }}>
                    {node.path.split("/").pop()}
                  </h3>
                  <p className="text-muted-foreground text-sm leading-relaxed mb-3">{node.refined_summary}</p>

                  {/* Node metadata */}
                  <div className="flex items-center gap-3">
                    <Badge variant="secondary" className="text-sm font-medium px-3 py-1">
                      {node.country}
                    </Badge>
                    <Badge variant="outline" className="text-sm font-medium px-3 py-1">
                      {node.record_count} records
                    </Badge>
                    {selectionState !== "none" && (
                      <Badge
                        variant="default"
                        className="text-sm font-medium px-3 py-1 bg-primary/10 text-primary border-primary/20"
                      >
                        {selectionState === "all" ? "All selected" : "Partially selected"}
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Expanded content */}
            {isExpanded && (
              <div className="mt-6" onClick={(e) => e.stopPropagation()}>
                {/* Records for this node */}
                {nodeRecords.length > 0 && (
                  <div className="mb-6">
                    <Separator className="mb-4" />
                    <div className="flex items-center gap-2 mb-4">
                      <FileText className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium text-muted-foreground">Records ({nodeRecords.length})</span>
                    </div>

                    <div className="space-y-3 pl-2">
                      {nodeRecords.map((record, index) => {
                        const isSelected = selectedRecords.has(record.id)

                        return (
                          <div key={record.id}>
                            <Card
                              className={`bg-background border-l-4 shadow-sm transition-all duration-200 ${
                                isSelected
                                  ? "border-l-primary border-primary/40 bg-primary/5"
                                  : "border-l-primary/20 hover:border-l-primary/40 hover:shadow-md cursor-pointer"
                              }`}
                            >
                              <CardContent className="p-3">
                                <div className="flex items-start gap-3">
                                  {/* Large Checkbox */}
                                  <div className="pt-0.5">
                                    <Checkbox
                                      checked={isSelected}
                                      onCheckedChange={() => onRecordToggle(record.id)}
                                      className="h-5 w-5"
                                    />
                                  </div>

                                  {/* Record Content */}
                                  <div className="flex-1 min-w-0 cursor-pointer" onClick={() => onRecordSelect(record)}>
                                    <div className="flex items-start justify-between mb-2">
                                      <div className="flex-1 min-w-0 mr-3">
                                        <h4 className="font-medium text-sm text-foreground line-clamp-1 mb-1">
                                          {record.fields.Title || `Record #${record.fields.ID}`}
                                        </h4>
                                        <p className="text-xs text-muted-foreground line-clamp-2">
                                          {record.fields["Cleaned Body"].substring(0, 120)}...
                                        </p>
                                      </div>

                                      <div className="flex items-center gap-2 shrink-0">
                                        <div
                                          className={`px-2 py-1 rounded text-white text-xs font-medium ${getBM42ScoreColor(record._bm42_score)}`}
                                        >
                                          {record._bm42_score.toFixed(1)}
                                        </div>
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          className="h-6 w-6 p-0 hover:bg-primary/10"
                                          onClick={(e) => {
                                            e.stopPropagation()
                                            window.open(record.fields["HTML URL"], "_blank")
                                          }}
                                        >
                                          <ExternalLink className="h-3 w-3" />
                                        </Button>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </CardContent>
                            </Card>
                          </div>
                        )
                      })}
                    </div>
                  </div>
                )}

                {/* Child nodes */}
                {node.children.length > 0 && (
                  <div className="space-y-4 pl-4">{node.children.map((child) => renderNode(child, level + 1))}</div>
                )}

                {/* Show message when expanded but no records and no children */}
                {nodeRecords.length === 0 && node.children.length === 0 && (
                  <div className="text-center py-6 text-muted-foreground">
                    <FileText className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">No records or child nodes</p>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className={`h-full flex flex-col bg-background ${selectedRecords.size > 0 ? "pb-64" : ""}`}>
      {/* Search Header */}
      <div className="p-4 border-b border-border">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
          <Input
            placeholder="Filter nodes..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-12 h-12 text-base bg-muted/30 border-muted"
          />
        </div>
      </div>

      {/* Nodes List */}
      <div ref={scrollContainerRef} className="flex-1 overflow-auto p-4">
        {filteredNodes.length === 0 ? (
          <div className="text-center text-muted-foreground py-8">
            <Folder className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p className="text-sm">No nodes found</p>
          </div>
        ) : (
          <div className="space-y-0">{filteredNodes.map((node) => renderNode(node, 0))}</div>
        )}
      </div>
    </div>
  )
}
