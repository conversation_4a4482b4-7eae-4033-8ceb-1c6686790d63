"use client"

import { useState, useEffect } from "react"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, Filter, History, Settings, ExternalLink } from "lucide-react"
import type { SearchState, Node, Record } from "@/types"

interface SearchPanelProps {
  searchState: SearchState
  onSearch: (query: string, mode: SearchState["mode"], filters: SearchState["filters"]) => void
  onResultSelect: (result: Node | Record) => void
}

export function SearchPanel({ searchState, onSearch, onResultSelect }: SearchPanelProps) {
  const [query, setQuery] = useState("")
  const [mode, setMode] = useState<SearchState["mode"]>("records")
  const [filters, setFilters] = useState<SearchState["filters"]>({})
  const [showAdvanced, setShowAdvanced] = useState(false)

  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      if (query.trim()) {
        onSearch(query, mode, filters)
      }
    }, 300)

    return () => clearTimeout(debounceTimer)
  }, [query, mode, filters, onSearch])

  const getBM42ScoreColor = (score: number) => {
    if (score >= 8) return "bg-green-500"
    if (score >= 6) return "bg-yellow-500"
    return "bg-red-500"
  }

  const getMethodBadgeVariant = (method: string) => {
    switch (method) {
      case "bm42":
        return "default"
      case "text-search":
        return "secondary"
      case "urls":
        return "outline"
      default:
        return "secondary"
    }
  }

  return (
    <div className="h-full flex flex-col">
      <div className="p-4 border-b border-border">
        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search with AI-powered BM42..."
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        <Tabs value={mode} onValueChange={(value) => setMode(value as SearchState["mode"])}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="nodes">Nodes</TabsTrigger>
            <TabsTrigger value="records">Records</TabsTrigger>
            <TabsTrigger value="urls">URLs</TabsTrigger>
          </TabsList>
        </Tabs>

        <div className="flex items-center gap-2 mt-4">
          <Button variant="outline" size="sm" onClick={() => setShowAdvanced(!showAdvanced)}>
            <Filter className="h-4 w-4 mr-1" />
            Filters
          </Button>
          <Button variant="outline" size="sm">
            <History className="h-4 w-4 mr-1" />
            History
          </Button>
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4" />
          </Button>
        </div>

        {showAdvanced && (
          <div className="mt-4 space-y-3 p-3 border border-border rounded-lg">
            <div>
              <label className="text-sm font-medium mb-1 block">Country</label>
              <Select
                value={filters.country || ""}
                onValueChange={(value) => setFilters({ ...filters, country: value || undefined })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All countries" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All countries</SelectItem>
                  <SelectItem value="USA">USA</SelectItem>
                  <SelectItem value="UK">UK</SelectItem>
                  <SelectItem value="Canada">Canada</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium mb-1 block">Path Contains</label>
              <Input
                placeholder="e.g., Payment/Online"
                value={filters.path || ""}
                onChange={(e) => setFilters({ ...filters, path: e.target.value || undefined })}
              />
            </div>
          </div>
        )}
      </div>

      <div className="flex-1 overflow-auto p-4">
        {searchState.isLoading ? (
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-muted rounded w-1/2"></div>
              </div>
            ))}
          </div>
        ) : searchState.results.length === 0 && query ? (
          <div className="text-center text-muted-foreground py-8">
            <Search className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No results found</p>
            <p className="text-sm">Try adjusting your search terms or filters</p>
          </div>
        ) : (
          <div className="space-y-3">
            {searchState.results.map((result, index) => {
              const isNode = "path" in result
              const node = result as Node
              const record = result as Record

              return (
                <Card
                  key={isNode ? node.id : record.id}
                  className="cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() => onResultSelect(result)}
                >
                  <CardContent className="p-3">
                    {isNode ? (
                      <div>
                        <div className="font-medium text-sm mb-1">{node.path.split("/").pop()}</div>
                        <div className="text-xs text-muted-foreground mb-2">{node.path}</div>
                        <p className="text-xs line-clamp-2 mb-2">{node.refined_summary || node.summary}</p>
                        <div className="flex items-center gap-2">
                          <Badge variant="secondary" className="text-xs">
                            {node.country}
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {node.record_count} records
                          </Badge>
                        </div>
                      </div>
                    ) : (
                      <div>
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <span className="font-medium text-sm">#{record.fields.ID}</span>
                            <a
                              href={record.fields["HTML URL"]}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-primary hover:text-primary/80"
                              onClick={(e) => e.stopPropagation()}
                            >
                              <ExternalLink className="h-3 w-3" />
                            </a>
                          </div>
                          <div className="flex items-center gap-1">
                            <Badge variant={getMethodBadgeVariant(record._search_method)} className="text-xs">
                              {record._search_method}
                            </Badge>
                            <div
                              className={`px-1.5 py-0.5 rounded text-white text-xs font-medium ${getBM42ScoreColor(record._bm42_score)}`}
                            >
                              {record._bm42_score.toFixed(1)}
                            </div>
                          </div>
                        </div>

                        <div className="text-xs text-muted-foreground mb-2">{record.fields.Breadcrumb}</div>

                        <p className="text-xs line-clamp-2 mb-2">{record.fields["Cleaned Body"]}</p>

                        <div className="flex items-center gap-1">
                          <Badge variant="secondary" className="text-xs">
                            {record.fields.Country}
                          </Badge>
                          {record.fields["Is Deepest"] === "Yes" && (
                            <Badge variant="default" className="text-xs">
                              Deepest
                            </Badge>
                          )}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )
            })}
          </div>
        )}
      </div>

      {searchState.results.length > 0 && (
        <div className="p-4 border-t border-border text-sm text-muted-foreground">
          {searchState.results.length} results found
        </div>
      )}
    </div>
  )
}
