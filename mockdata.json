{"children": {"Docs Team [Internal]": {"children": {"Draft Article by AI": {"children": {}, "records": [{"id": "recrLpy9zxXijhomG", "fields": {"ID": **************, "HTML URL": "https://help.xendit.co/hc/en-us/articles/**************-Cards-Chargeback-Case-Handling-Philippines", "Title": "Cards - Chargeback Case Handling - Philippines", "Cleaned Body": "## Typical Customer Issues\n* Merchants may inquire about the status of chargeback cases.\n* Requests for additional documentation or evidence needed to support chargeback disputes.\n* Questions regarding the automation process for chargeback handling.\n\n## Probe\n* Gather the transaction ID related to the chargeback.\n* Request any additional details from the customer that may help in processing the chargeback.\n* Verify if there's relevant documentation that the merchant has submitted for the dispute.", "Breadcrumb": "Category: Docs Team [Internal] // Section: Draft Article by AI", "Is Deepest": "true", "Country": "general", "Section ID": **************}}, {"id": "recr5zuNdxBeDpzoY", "fields": {"ID": **************, "HTML URL": "https://help.xendit.co/hc/en-us/articles/**************-Withdrawal-Process-Overview-for-Customer-Success", "Title": "Withdrawal Process Overview for Customer Success", "Cleaned Body": "This guide provides an overview of the withdrawal process for customer success teams. It covers the standard procedures, common issues, and escalation paths when handling withdrawal requests from merchants.", "Breadcrumb": "Category: Docs Team [Internal] // Section: Draft Article by AI", "Is Deepest": "true", "Country": "general", "Section ID": **************}}, {"id": "rectba6hZtnw1AiAE", "fields": {"ID": 46515446660889, "HTML URL": "https://help.xendit.co/hc/en-us/articles/46515446660889-Dashboard-Issues-Mapping-and-Escalation-Process", "Title": "Dashboard Issues Mapping and Escalation Process", "Cleaned Body": "This document outlines the process for mapping dashboard issues and the appropriate escalation procedures. It includes troubleshooting steps, common error scenarios, and when to involve technical teams.", "Breadcrumb": "Category: Docs Team [Internal] // Section: Draft Article by AI", "Is Deepest": "true", "Country": "general", "Section ID": **************}}], "id": "ba9233fa-39a8-4edc-8112-0b220a3197dc", "country": "general", "summary": "AI-generated draft articles for internal documentation", "refinedSummary": "Draft Article by AI covers troubleshooting and resolving issues related to payment processing (including manual OTC payments via Cebuana, failed e-wallet transactions with Trojans Well, and QRPH transactions), merchant onboarding, Xendit Checkout integration with Cloudbeds, disbursement failures (including bouncebacks and UBP Connectors like Instapay and Pesonet), and merchant inquiries about topics like Bill Payment, Withholding Tax Slips, subscription plans, and accessing old data reports, often involving Admin Dashboard checks, Retool applications, and escalation to internal teams like Merchant Risk."}, "Customer Support Guidelines": {"children": {}, "records": [{"id": "rec_cs_001", "fields": {"ID": 47233790870042, "HTML URL": "https://help.xendit.co/hc/en-us/articles/customer-support-escalation", "Title": "Customer Support Escalation Matrix", "Cleaned Body": "This document defines the escalation matrix for customer support issues. It outlines when to escalate to different teams based on issue severity, customer tier, and technical complexity.", "Breadcrumb": "Category: Docs Team [Internal] // Section: Customer Support Guidelines", "Is Deepest": "true", "Country": "general", "Section ID": 41880529878426}}], "id": "cs-guidelines-001", "country": "general", "summary": "Guidelines and procedures for customer support teams", "refinedSummary": "Comprehensive guidelines for customer support teams including escalation procedures, response time requirements, and best practices for handling various types of customer inquiries."}}, "records": [{"id": "rec_internal_001", "fields": {"ID": 47233790870040, "HTML URL": "https://help.xendit.co/hc/en-us/articles/internal-team-overview", "Title": "Internal Team Documentation Overview", "Cleaned Body": "This is the main overview document for all internal team documentation. It provides an index of available resources and guidelines for contributing to the knowledge base.", "Breadcrumb": "Category: Docs Team [Internal]", "Is Deepest": "false", "Country": "general", "Section ID": **************}}], "id": "6485c9fa-e5f0-4b32-90c2-89d8ed283c3b", "country": "general", "summary": "Internal documentation and guidelines", "refinedSummary": "Internal documentation hub containing guidelines, procedures, and resources for internal teams including customer support, technical documentation, and operational procedures."}, "Payment Processing": {"children": {"Credit Card": {"children": {"Visa Integration": {"children": {}, "records": [{"id": "rec_visa_001", "fields": {"ID": **************, "HTML URL": "https://help.xendit.co/hc/en-us/articles/visa-direct-api-integration", "Title": "Visa Direct API Integration Guide", "Cleaned Body": "Complete guide for integrating Visa Direct API for real-time payments. Covers authentication, endpoint configuration, error handling, and testing procedures.", "Breadcrumb": "Category: Payment Processing // Section: Credit Card // Subsection: Visa Integration", "Is Deepest": "true", "Country": "global", "Section ID": **************}}], "id": "visa-integration-001", "country": "global", "summary": "Visa payment integration documentation", "refinedSummary": "Detailed implementation guides for Visa payment processing including Direct API integration, webhook handling, and fraud prevention tools."}}, "records": [{"id": "rec_cc_001", "fields": {"ID": **************, "HTML URL": "https://help.xendit.co/hc/en-us/articles/credit-card-setup", "Title": "Credit Card Processing Setup", "Cleaned Body": "How to set up credit card processing for your application. Credit card processing requires merchant account configuration with your payment processor. Begin by obtaining API credentials from your payment gateway, then implement secure token handling for sensitive card data.", "Breadcrumb": "Category: Payment Processing // Section: Credit Card", "Is Deepest": "false", "Country": "global", "Section ID": **************}}], "id": "credit-card-001", "country": "global", "summary": "Credit card payment processing guides", "refinedSummary": "Comprehensive documentation for implementing credit card payment processing, including security protocols, PCI compliance, transaction flows, and integration examples for major payment processors."}}, "records": [], "id": "payment-processing-001", "country": "global", "summary": "Payment processing documentation", "refinedSummary": "Complete payment processing documentation covering various payment methods, integration guides, security requirements, and best practices for handling financial transactions."}, "API Documentation": {"children": {"REST APIs": {"children": {"Authentication": {"children": {"OAuth 2.0": {"children": {}, "records": [{"id": "rec_oauth_001", "fields": {"ID": **************, "HTML URL": "https://help.xendit.co/hc/en-us/articles/oauth-client-credentials-flow", "Title": "OAuth 2.0 Client Credentials Flow Implementation", "Cleaned Body": "Detailed guide for implementing OAuth 2.0 Client Credentials Flow for server-to-server authentication. This flow is ideal for backend services that need to authenticate without user interaction. Includes code examples, security considerations, and token management best practices.", "Breadcrumb": "Category: API Documentation // Section: REST APIs // Subsection: Authentication // OAuth 2.0", "Is Deepest": "true", "Country": "global", "Section ID": 41880529878460}}, {"id": "rec_oauth_002", "fields": {"ID": 47233790870061, "HTML URL": "https://help.xendit.co/hc/en-us/articles/oauth-authorization-code-flow", "Title": "OAuth 2.0 Authorization Code Flow for Web Applications", "Cleaned Body": "Complete implementation guide for OAuth 2.0 Authorization Code Flow in web applications. Covers PKCE implementation, state parameter usage, token refresh mechanisms, and security best practices for protecting user data.", "Breadcrumb": "Category: API Documentation // Section: REST APIs // Subsection: Authentication // OAuth 2.0", "Is Deepest": "true", "Country": "global", "Section ID": 41880529878460}}], "id": "oauth-2-001", "country": "global", "summary": "OAuth 2.0 authentication implementation guides", "refinedSummary": "Comprehensive OAuth 2.0 implementation documentation covering all major flows including Client Credentials, Authorization Code, and PKCE. Includes security best practices, token management, and real-world integration examples."}, "JWT Tokens": {"children": {}, "records": [{"id": "rec_jwt_001", "fields": {"ID": 47233790870062, "HTML URL": "https://help.xendit.co/hc/en-us/articles/jwt-token-validation", "Title": "JWT Token Validation and Security Best Practices", "Cleaned Body": "Essential guide for JWT token validation including signature verification, claims validation, and security considerations. Covers common vulnerabilities like algorithm confusion attacks and provides implementation examples for secure JWT handling.", "Breadcrumb": "Category: API Documentation // Section: REST APIs // Subsection: Authentication // JWT Tokens", "Is Deepest": "true", "Country": "global", "Section ID": 41880529878461}}], "id": "jwt-tokens-001", "country": "global", "summary": "JWT token implementation and security guides", "refinedSummary": "Complete JWT token documentation including creation, validation, security best practices, and integration examples for modern authentication systems."}}, "records": [], "id": "authentication-001", "country": "global", "summary": "API authentication methods and security", "refinedSummary": "Comprehensive API authentication documentation covering OAuth 2.0, JWT tokens, API keys, and security best practices for secure API access and user authentication."}}, "records": [{"id": "rec_rest_001", "fields": {"ID": 47233790870063, "HTML URL": "https://help.xendit.co/hc/en-us/articles/rest-api-design-principles", "Title": "REST API Design Principles and Best Practices", "Cleaned Body": "Fundamental principles for designing RESTful APIs including resource modeling, HTTP method usage, status codes, versioning strategies, and documentation standards. Essential reading for API developers and architects.", "Breadcrumb": "Category: API Documentation // Section: REST APIs", "Is Deepest": "false", "Country": "global", "Section ID": 41880529878462}}], "id": "rest-apis-001", "country": "global", "summary": "RESTful API development guides", "refinedSummary": "Complete REST API documentation including design principles, implementation guides, authentication methods, error handling, and best practices for building scalable and maintainable APIs."}}, "records": [], "id": "api-documentation-001", "country": "global", "summary": "API development and integration guides", "refinedSummary": "Comprehensive API documentation covering REST APIs, GraphQL, authentication, rate limiting, versioning, and integration best practices for developers and technical teams."}}}