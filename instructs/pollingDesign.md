# Evaluation System Documentation

## Overview

The evaluation system is a sophisticated AI-powered groundedness analysis feature that assesses the quality and accuracy of AI-generated responses by analyzing how well each claim in the response is supported by the provided context. The system operates in two modes: synchronous (immediate) and asynchronous (background with polling) to optimize user experience.

## System Architecture

### Core Components

1. **EvaluationService** (`lib/evaluation-service.ts`) - Core evaluation engine
2. **Evaluation APIs** (`app/api/evaluation*/`) - RESTful endpoints
3. **Frontend Polling** (`app/page.tsx`) - Asynchronous result retrieval
4. **Response Modal** (`components/response-modal.tsx`) - UI display
5. **Utility Functions** (`lib/evaluation-utils.ts`) - Helper functions

### Data Flow Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   User Query    │───▶│   Gemini API     │───▶│ AI Response     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ Frontend Poll   │◀──▶│ Evaluation API   │◀───│ Background Eval │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## Operating Modes

### 1. Synchronous Mode (ENABLE_BACKGROUND_EVAL=false)

**Flow:**

1. User submits query
2. Generate AI response (3-4 seconds)
3. Evaluate response immediately (2-3 seconds)
4. Return complete data (total: 5-7 seconds)
5. Display modal with all data

**Advantages:**

- Simple implementation
- Complete data immediately available
- No polling complexity

**Disadvantages:**

- Longer perceived wait time
- Users see no progress during evaluation

### 2. Asynchronous Mode (ENABLE_BACKGROUND_EVAL=true) - **Recommended**

**Flow:**

1. User submits query
2. Generate AI response (3-4 seconds)
3. Return response immediately with `requestId`
4. Display modal with response + evaluation spinner
5. **Background:** Start evaluation process
6. **Background:** Store evaluation result in cache
7. **Frontend:** Poll every 2 seconds for evaluation completion
8. Update modal when evaluation completes

**Advantages:**

- Fast perceived response time
- Progressive loading UX
- Better user engagement

**Disadvantages:**

- More complex implementation
- Requires polling mechanism
- Potential for evaluation timeouts

## API Endpoints

### `/api/gemini` (POST)

**Purpose:** Main endpoint for AI response generation with optional evaluation

**Request Body:**

```json
{
  "query": "What is renewable energy?",
  "selectedRecords": [...],
  "includeEvaluation": true
}
```

**Response (Async Mode):**

```json
{
  "response": "AI generated response text...",
  "recordsUsed": 3,
  "evaluationStatus": "processing",
  "requestId": "eval_1750668570149_i0l51hl92"
}
```

**Response (Sync Mode):**

```json
{
  "response": "AI generated response text...",
  "recordsUsed": 3,
  "evaluation": {
    "claim_analysis": [...],
    "overall_score": 85,
    "evaluation_metadata": {...}
  },
  "evaluationTimestamp": "2024-01-23T10:30:00Z"
}
```

**Key Features:**

- Generates unique `requestId` in format: `eval_{timestamp}_{randomString}`
- Uses `setImmediate()` for non-blocking background evaluation
- Stores results via internal API call to `/api/evaluation-status`

### `/api/evaluation-status` (GET/POST)

**Purpose:** Manage evaluation results for polling mechanism

**GET `/api/evaluation-status?id={requestId}`**

_Query Parameters:_

- `id`: Request ID from initial API call

_Response (Processing):_

```json
{
  "status": "processing"
}
```

_Response (Completed):_

```json
{
  "status": "completed",
  "evaluation": {
    "claim_analysis": [...],
    "overall_score": 85,
    "evaluation_metadata": {...}
  }
}
```

**POST `/api/evaluation-status`**

_Request Body:_

```json
{
  "requestId": "eval_1750668570149_i0l51hl92",
  "evaluation": {
    "claim_analysis": [...],
    "overall_score": 85,
    "evaluation_metadata": {...}
  }
}
```

**Storage Mechanism:**

- Uses in-memory `Map<string, any>` for demo purposes
- In production, should use Redis or database
- No expiration mechanism (evaluations persist until server restart)

### `/api/evaluation` (GET/POST)

**Purpose:** Direct evaluation endpoint for batch processing and health checks

**POST `/api/evaluation`** - Single Evaluation

```json
{
  "question": "What is renewable energy?",
  "answer": "Renewable energy is clean power...",
  "context": "Solar energy provides..."
}
```

**POST `/api/evaluation`** - Batch Evaluation

```json
{
  "batch": true,
  "evaluations": [
    {
      "question": "Question 1",
      "answer": "Answer 1",
      "context": "Context 1"
    },
    {
      "question": "Question 2",
      "answer": "Answer 2",
      "context": "Context 2"
    }
  ]
}
```

**GET `/api/evaluation`** - Health Check
Returns service health status and Gemini API connectivity.

## Core Evaluation Engine

### EvaluationService Class

**Location:** `lib/evaluation-service.ts`

**Key Methods:**

- `evaluateGroundedness(request: EvaluationRequest): Promise<EvaluationResult>`
- `parseEvaluationResponse(responseText: string): ClaimAnalysis[]`
- `validateAndProcessClaims(claims: ClaimAnalysis[], context: string): ClaimAnalysis[]`
- `calculateOverallMetrics(claims: ClaimAnalysis[]): EvaluationResult`

### Evaluation Prompt Engineering

The system uses a sophisticated prompt that instructs Gemini 2.0 Flash to:

1. **Break down the AI response into distinct claims**
2. **For each claim:**

   - Extract supporting evidence from context
   - Format evidence as `{start_words, end_words}` snippets
   - Assign groundedness score (1-100)
   - Provide reasoning

3. **Scoring Rubric:**
   - **100:** Direct restatement of context
   - **70-99:** Fully supported, reasonable inference
   - **40-69:** Partially supported
   - **2-39:** Weakly related
   - **1:** Unsupported or contradictory
   - **0:** Hallucination (assigned programmatically)

### Hallucination Detection

**Process:**

1. LLM returns claims with supporting snippet boundaries
2. System attempts to extract actual snippets from context
3. If extraction fails, claim is marked as hallucination (score: 0)
4. Logs warning: `"Failed to extract snippet for claim X"`
5. Updates reasoning to indicate likely hallucination

**Example from logs:**

```
Failed to extract snippet for claim 0: "This is the main overview...team documentation overview document"
Marking claim 0 as hallucination due to failed snippet extraction
```

## Frontend Polling Implementation

### Polling Function

**Location:** `app/page.tsx:161-200`

```typescript
const pollForEvaluationResults = async (requestId: string) => {
  const maxAttempts = 20; // 40 seconds total (20 * 2s intervals)
  let attempts = 0;
  let isPolling = true;

  const poll = async () => {
    if (!isPolling) return; // Cancellation check

    try {
      const response = await fetch(`/api/evaluation-status?id=${requestId}`);
      const data = await response.json();

      if (data.status === "completed" && data.evaluation) {
        // Update UI with evaluation data
        setLlmResponse((prev) =>
          prev
            ? {
                ...prev,
                evals: {
                  claim_analysis: data.evaluation.claim_analysis || [],
                },
              }
            : null
        );
        setIsEvaluationLoading(false);
        isPolling = false;
        return;
      }

      // Continue polling
      attempts++;
      if (attempts < maxAttempts && isPolling) {
        setTimeout(poll, 2000); // 2-second intervals
      } else {
        console.warn(`Evaluation polling timed out for request: ${requestId}`);
        setIsEvaluationLoading(false);
        isPolling = false;
      }
    } catch (error) {
      console.error("Error polling for evaluation results:", error);
      setIsEvaluationLoading(false);
      isPolling = false;
    }
  };

  setTimeout(poll, 1000); // Start after 1 second delay

  return () => {
    isPolling = false;
  }; // Cleanup function
};
```

**Key Features:**

- **2-second polling interval** for optimal balance of responsiveness and server load
- **40-second timeout** (20 attempts) with graceful fallback
- **Cancellation support** via cleanup function
- **Error handling** with automatic polling termination
- **1-second initial delay** to allow background evaluation to start

### State Management

**Evaluation States:**

- `isEvaluationLoading: boolean` - Shows spinner in UI
- `currentPollingCleanup: (() => void) | null` - Cleanup function for active polling
- `llmResponse.evals.claim_analysis` - Evaluation results array

**Lifecycle Management:**

```typescript
// Start polling
const cleanup = pollForEvaluationResults(data.requestId);
setCurrentPollingCleanup(() => cleanup);

// Cleanup on component unmount
useEffect(() => {
  return () => {
    if (currentPollingCleanup) {
      currentPollingCleanup();
    }
  };
}, []);

// Cleanup when starting new evaluation
if (currentPollingCleanup) {
  currentPollingCleanup();
  setCurrentPollingCleanup(null);
}
```

## User Interface

### Response Modal Integration

**Location:** `components/response-modal.tsx`

**Features:**

- **Evaluation Overview Card** - Overall score and summary statistics
- **Claims Analysis Section** - Individual claim breakdown
- **Supporting Evidence** - Context snippets with highlighting
- **Loading States** - Spinner during evaluation with "Evaluating response claims..." message
- **Error States** - "Evaluation data not available" fallback

**Data Structure:**

```typescript
interface LlmResponse {
  response: string;
  evals: {
    claim_analysis: ClaimAnalysis[];
  };
}

interface ClaimAnalysis {
  claim: string;
  supporting_context_snippets: Array<{
    start_words: string;
    end_words: string;
  }>;
  reasoning: string;
  groundedness_score: number;
}
```

### Score Display System

**Score Ranges:**

- **90-100:** Excellent (Green badge)
- **70-89:** Good (Blue badge)
- **40-69:** Fair (Yellow badge)
- **1-39:** Poor (Red badge)
- **0:** Hallucination (Red badge with special marking)

**Visual Indicators:**

- **Color-coded badges** for overall score
- **Individual claim scores** with reasoning
- **Supporting evidence highlighting** (when available)
- **Progress spinner** during evaluation

## Configuration & Environment

### Environment Variables

**Required:**

- `GEMINI_API_KEY` - Google Gemini API key for evaluation service

**Optional:**

- `ENABLE_BACKGROUND_EVAL=true` - Enable asynchronous evaluation mode
- `NEXT_PUBLIC_BASE_URL` - Base URL for internal API calls (defaults to localhost:3000)

### Performance Configuration

**Gemini API Settings:**

```typescript
{
  model: 'gemini-2.0-flash',
  config: {
    temperature: 0.1,        // Low temperature for consistent evaluation
    maxOutputTokens: 1536,   // Reduced from 4096 for faster processing
  }
}
```

**Polling Configuration:**

- **Interval:** 2 seconds
- **Timeout:** 40 seconds (20 attempts)
- **Initial delay:** 1 second

**Batch Evaluation:**

- **Concurrency limit:** 3 simultaneous evaluations
- **Chunked processing** to avoid API rate limits

## Error Handling & Debugging

### Common Error Scenarios

1. **API Key Missing/Invalid**

   - Error: "Evaluation failed: [Gemini API error]"
   - Solution: Verify `GEMINI_API_KEY` environment variable

2. **Evaluation Timeout**

   - Error: "Evaluation polling timed out for request: eval\_..."
   - Cause: Background evaluation taking longer than 40 seconds
   - Solution: Check Gemini API status, server load

3. **Snippet Extraction Failure**

   - Error: "Failed to extract snippet for claim X"
   - Cause: LLM hallucinating evidence that doesn't exist in context
   - Automatic handling: Claim marked as hallucination (score: 0)

4. **Network Issues**
   - Error: "Error polling for evaluation results"
   - Cause: Network connectivity or server restart
   - Handling: Polling stops gracefully with error state

### Debug Logging

**Console Logs to Monitor:**

```bash
# Successful evaluation completion
"Background evaluation completed for request: eval_1750668570149_i0l51hl92"

# Polling activity
GET /api/evaluation-status?id=eval_1750668570149_i0l51hl92 200

# Snippet extraction issues
"Failed to extract snippet for claim 0: 'This is the main overview...'"
"Marking claim 0 as hallucination due to failed snippet extraction"

# Polling timeout
"Evaluation polling timed out for request: eval_1750668570149_i0l51hl92"
```

**Network Monitoring:**

- Monitor `/api/evaluation-status` GET requests (should occur every 2 seconds)
- Check for 200 responses vs error codes
- Verify POST requests to store evaluation results

## Testing & Validation

### Functional Testing

1. **Basic Evaluation Test:**

   - Select 1-3 records from tree view
   - Submit query and verify 3-4 second response time
   - Confirm evaluation spinner appears
   - Verify claims analysis populates automatically

2. **Background Evaluation Test:**

   - Set `ENABLE_BACKGROUND_EVAL=true`
   - Monitor console for "Background evaluation completed" messages
   - Check network tab for `/api/evaluation-status` polling calls
   - Verify seamless UI update when evaluation completes

3. **Error Handling Test:**
   - Test with invalid API key
   - Test network disconnection during polling
   - Verify graceful degradation

### Load Testing Considerations

- **Polling frequency:** 2-second intervals minimize server load
- **Timeout limits:** 40-second maximum prevents infinite polling
- **Batch processing:** 3-request concurrency limit for API protection
- **Memory usage:** In-memory cache grows with each evaluation (production should use Redis)

## Production Deployment Recommendations

### Infrastructure

1. **Database Integration:**

   - Replace in-memory cache with Redis or PostgreSQL
   - Add TTL for evaluation results (e.g., 24 hours)
   - Implement result persistence for audit trails

2. **Monitoring:**

   - Add metrics for evaluation success/failure rates
   - Monitor average evaluation response times
   - Track polling timeout frequency

3. **Rate Limiting:**
   - Implement rate limits for evaluation endpoints
   - Add request queuing for high-load scenarios
   - Consider caching for identical question/context pairs

### Security Considerations

1. **API Key Management:**

   - Use secure environment variable management
   - Rotate keys regularly
   - Monitor API usage for anomalies

2. **Input Validation:**
   - Sanitize user queries and context
   - Limit context size to prevent excessive API costs
   - Validate request IDs to prevent enumeration attacks

## Performance Metrics

### Current Performance

- **AI Response Generation:** 3-4 seconds
- **Evaluation Processing:** 2-3 seconds
- **Background Mode Total UX:** 3-4 seconds (perceived), ~6-7 seconds (actual)
- **Polling Frequency:** Every 2 seconds
- **Timeout:** 40 seconds maximum

### Optimization Opportunities

1. **Token Reduction:**

   - Current: 1536 max output tokens
   - Consider dynamic token allocation based on response length

2. **Caching:**

   - Cache evaluation results for identical question/answer/context combinations
   - Implement semantic similarity matching for near-duplicates

3. **Parallel Processing:**
   - Process multiple claims simultaneously within single evaluation
   - Batch multiple evaluations when possible

## Future Enhancements

### Planned Features

1. **Advanced Scoring:**

   - Weighted scoring based on claim importance
   - Domain-specific evaluation criteria
   - Confidence intervals for scores

2. **Enhanced UI:**

   - Real-time claim-by-claim evaluation progress
   - Interactive evidence highlighting
   - Historical evaluation trends

3. **Analytics Integration:**
   - Evaluation quality metrics dashboard
   - User behavior analytics for evaluation usage
   - A/B testing for different evaluation strategies

### Scalability Considerations

1. **Horizontal Scaling:**

   - Stateless evaluation service design
   - Load balancer configuration for multiple instances
   - Distributed caching layer

2. **API Optimization:**
   - Response compression for large evaluation results
   - Streaming responses for real-time updates
   - WebSocket integration for instant updates

This evaluation system represents a sophisticated approach to AI response quality assessment, balancing accuracy, performance, and user experience through intelligent architecture and implementation choices.
