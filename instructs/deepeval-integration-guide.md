`````markdown
# Custom LLM Evaluation System - Implementation Guide

## Overview

This guide outlines the implementation of a custom LLM-based evaluation system for assessing the groundedness and quality of AI-generated responses in our Next.js knowledge base application. Instead of using external evaluation frameworks, we implement a prompt-based evaluation system that provides detailed scoring, evidence extraction, and reasoning.

## Quick Start

### 1. Review Project Documentation

Before implementation, review the following documents:

- **Tasks**: `tasks/custom-llm-evaluation-implementation.md` - Detailed implementation tasks and timeline
- **Implementation Log**: `instructs/custom-llm-evaluation-implementation-log.md` - Track progress and decisions

### 2. Implementation Workflow

#### Phase 1: Core Evaluation Engine (Week 1-2)

Follow tasks 1.1-1.3 in the task document:

- Set up evaluation service infrastructure
- Implement claim analysis engine using the evaluation prompt below
- Build scoring and reasoning validation

#### Phase 2: API Integration (Week 3)

Follow tasks 2.1-2.2 to integrate with existing application:

- Create evaluation endpoints
- Enhance Gemini API with optional evaluation
- Configure environment variables

#### Phase 3: User Interface (Week 4)

Follow tasks 3.1-3.2 to build evaluation display:

- Create evaluation panel components
- Integrate with existing UI
- Add evaluation controls

#### Phase 4: Data & Analytics (Week 5)

Follow tasks 4.1-4.2 for data management:

- Implement evaluation storage
- Build analytics and reporting
- Add monitoring capabilities

## Core Evaluation Prompt

Use this prompt template in your LLM evaluation service:

````markdown
## Primary Goal

You are an expert AI evaluator. Your purpose is to meticulously analyze a generated `Answer` and determine how well each of its claims is grounded in the provided `Context`. You will not evaluate the factual accuracy of the `Context` itself, but only whether the `Answer` is faithfully supported by it.

## Instructions

1.  **Identify Claims**: First, break down the `Answer` into a list of distinct, verifiable claims. A claim is a single, factual statement.
2.  **Analyze Each Claim**: For each individual claim, you must perform the following steps:
    - **Extract Evidence**: Thoroughly search the `Context` to identify the continuous text snippet(s) that act as evidence. For each snippet you identify, you must format it as follows:
      - Create an object containing `start_words` and `end_words`.
      - **`start_words`**: The first 5 words of the supporting snippet.
      - **`end_words`**: The last 5 words of the supporting snippet.
      - **Rule for short snippets**: If a snippet has 10 words or fewer, use the full snippet text for both the `start_words` and `end_words` fields.
      - If no supporting evidence is found, `supporting_context_snippets` should be an empty list `[]`.
    - **Assign Score**: Assign a `groundedness_score` from 1 to 100 for the claim based on its relationship to the `Context`. Use the following scoring rubric:
      - **Score 100**: The claim is a direct restatement or a perfect logical paraphrase of information stated explicitly in the `Context`.
      - **Score 70-99**: The claim is fully supported and a reasonable inference from the `Context`, but not stated verbatim.
      - **Score 40-69**: The claim is **partially supported**. The `Context` provides evidence for the general idea, but the claim is stronger or more specific than what is explicitly stated.
      - **Score 2-39**: The claim is **weakly related** to the `Context`, requiring a significant logical leap.
      - **Score 1**: The claim is **completely unsupported** by or directly **contradicts** the `Context`.
      - **Score 0**: **SYSTEM OVERRIDE** - Automatically assigned when programmatic validation detects that supporting evidence doesn't actually exist in the context (likely hallucination).
    - **Provide Reasoning**: Write a brief explanation for the score you assigned.
3.  **Format the Output**: Your final output MUST be a single JSON object containing the detailed analysis for all claims. Do not include any other text, notes, or explanations outside of this JSON structure.

## Prompt Template Variables

### Question

{{question}}

### Answer

{{answer}}

### Context

{{context}}

## Evaluation Output (JSON)

```json
{
  "claim_analysis": [
    {
      "claim": "The first claim made in the answer.",
      "supporting_context_snippets": [
        {
          "start_words": "The first five words of",
          "end_words": "the last five words here."
        }
      ],
      "reasoning": "The claim is a direct restatement of the information found in the provided context, hence the perfect score.",
      "groundedness_score": 100
    },
    {
      "claim": "A second claim from the answer which is a short snippet.",
      "supporting_context_snippets": [
        {
          "start_words": "This snippet is nine words.",
          "end_words": "This snippet is nine words."
        }
      ],
      "reasoning": "The supporting snippet is short, so the full text is provided in both fields.",
      "groundedness_score": 90
    },
    {
      "claim": "A third claim that is not supported by the context.",
      "supporting_context_snippets": [],
      "reasoning": "There is no information in the provided context that can substantiate this claim.",
      "groundedness_score": 1
    }
  ]
}
```
````

## Hallucination Detection & Validation

**Critical Security Feature**: The evaluation system includes programmatic validation to prevent LLM hallucinations in evaluation results.

### How It Works

The system performs automatic validation of all evidence snippets claimed by the LLM:

1. **Programmatic Extraction**: For each `supporting_context_snippets` entry, the system attempts to programmatically locate the claimed evidence in the actual context using the provided `start_words` and `end_words`.

2. **Validation Process**:

   - Searches for `start_words` in the context (case-insensitive)
   - Searches for `end_words` starting from the start position
   - Attempts to extract the full snippet between these boundaries
   - Validates snippet length and content

3. **Hallucination Detection**: If extraction fails (evidence doesn't exist), the system automatically:
   - Sets `groundedness_score: 0` (indicating likely hallucination)
   - Updates `reasoning` to: _"Likely hallucination: Supporting evidence could not be found in the provided context"_
   - Counts the claim as unsupported in overall metrics

### Special Cases Handled

- **Short Snippets (≤10 words)**: When `start_words` equals `end_words`, validates the full text exists in context
- **Case Sensitivity**: All matching is case-insensitive to handle formatting variations
- **Size Limits**: Prevents extraction of unreasonably long snippets (>1000 characters)
- **Edge Cases**: Handles empty text, special characters, and malformed input

### Example: Hallucination Detection

**Legitimate Evidence** (✅ Validated):

```json
{
  "claim": "Solar energy provides clean electricity",
  "supporting_context_snippets": [
    {
      "start_words": "Solar energy provides clean",
      "end_words": "electricity without emissions"
    }
  ],
  "reasoning": "Direct support found in context",
  "groundedness_score": 95
}
```

**Hallucinated Evidence** (❌ Caught & Corrected):

```json
// LLM Response (before validation)
{
  "claim": "Solar energy is expensive",
  "supporting_context_snippets": [
    {
      "start_words": "Solar energy costs are",  // Doesn't exist in context
      "end_words": "most expensive renewable"   // Doesn't exist in context
    }
  ],
  "reasoning": "Context mentions high costs",   // Original LLM reasoning
  "groundedness_score": 80                     // Original LLM score
}

// After Validation (automatically corrected)
{
  "claim": "Solar energy is expensive",
  "supporting_context_snippets": [
    {
      "start_words": "Solar energy costs are",
      "end_words": "most expensive renewable"
    }
  ],
  "reasoning": "Likely hallucination: Supporting evidence could not be found in the provided context",
  "groundedness_score": 0                      // Corrected to 0
}
```

### Security Benefits

1. **Prevents False Positives**: Eliminates fake evidence from evaluation results
2. **Maintains Trust**: Ensures all supporting evidence is actually grounded in context
3. **Transparent Failures**: Clear identification of hallucinated evidence
4. **Audit Trail**: Detailed logging of validation failures for debugging

### Implementation Details

The validation is implemented in `lib/evaluation-service.ts`:

```typescript
// Core validation method
private extractSnippetFromContext(context: string, startWords: string, endWords: string): string | null {
  // Handle short snippets (≤10 words) where start/end are identical
  if (startWords === endWords) {
    const words = startWords.trim().split(/\s+/);
    if (words.length <= 10) {
      const regex = new RegExp(this.escapeRegex(startWords), 'i');
      return regex.test(context) ? startWords : null;
    }
  }

  // Find start and end positions in context
  const startIndex = context.toLowerCase().indexOf(startWords.toLowerCase());
  const endIndex = context.toLowerCase().indexOf(endWords.toLowerCase(), startIndex);

  // Extract and validate the snippet
  if (startIndex !== -1 && endIndex !== -1 && endIndex > startIndex) {
    const snippet = context.substring(startIndex, endIndex + endWords.length);
    return snippet.length <= 1000 ? snippet.trim() : null;
  }

  return null; // Evidence not found - likely hallucination
}
```

### Programmatic Context Generation

The evaluation output can be used to programmatically generate supporting context snippets for display in your application:

#### Using Evaluation Output to Extract Context

```typescript
import type {
  EvaluationResult,
  ClaimAnalysis,
} from "../lib/evaluation-service";

/**
 * Extract actual supporting context snippets from evaluation results
 */
function extractSupportingContext(
  evaluation: EvaluationResult,
  originalContext: string
): Array<{
  claim: string;
  score: number;
  extractedSnippets: string[];
  reasoning: string;
}> {
  return evaluation.claim_analysis.map((claim) => {
    const extractedSnippets = claim.supporting_context_snippets
      .map((snippet) =>
        extractSnippet(originalContext, snippet.start_words, snippet.end_words)
      )
      .filter((snippet) => snippet !== null) as string[];

    return {
      claim: claim.claim,
      score: claim.groundedness_score,
      extractedSnippets,
      reasoning: claim.reasoning,
    };
  });
}

/**
 * Extract a single snippet from context using start and end words
 */
function extractSnippet(
  context: string,
  startWords: string,
  endWords: string
): string | null {
  // Handle short snippets (≤10 words) where start/end are identical
  if (startWords === endWords) {
    const words = startWords.trim().split(/\s+/);
    if (words.length <= 10) {
      const regex = new RegExp(escapeRegex(startWords), "i");
      return regex.test(context) ? startWords : null;
    }
  }

  // Find start and end positions
  const cleanContext = context.toLowerCase();
  const startIndex = cleanContext.indexOf(startWords.toLowerCase());
  if (startIndex === -1) return null;

  const endIndex = cleanContext.indexOf(endWords.toLowerCase(), startIndex);
  if (endIndex === -1) return null;

  // Extract from original context (preserve case/formatting)
  const snippetEnd = endIndex + endWords.length;
  if (snippetEnd <= startIndex) return null;

  const snippet = context.substring(startIndex, snippetEnd).trim();
  return snippet.length > 0 && snippet.length <= 1000 ? snippet : null;
}

function escapeRegex(text: string): string {
  return text.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
}
```

#### Example Usage: UI Context Highlighting

```typescript
// Example: Using extracted snippets for UI highlighting
async function displayEvaluationWithContext(
  question: string,
  answer: string,
  contextRecords: any[],
  evaluation: EvaluationResult
) {
  const originalContext = formatContextFromRecords(contextRecords);
  const supportingContext = extractSupportingContext(
    evaluation,
    originalContext
  );

  console.log(`📊 Evaluation Score: ${evaluation.overall_score}/100`);
  console.log(
    `📝 Claims Analyzed: ${evaluation.evaluation_metadata.total_claims}\n`
  );

  supportingContext.forEach((item, index) => {
    const scoreIcon =
      item.score >= 70
        ? "✅"
        : item.score >= 40
        ? "⚠️"
        : item.score === 0
        ? "🚫"
        : "❌";

    console.log(`${scoreIcon} Claim ${index + 1} (Score: ${item.score}/100):`);
    console.log(`   "${item.claim}"`);
    console.log(`   Reasoning: ${item.reasoning}`);

    if (item.extractedSnippets.length > 0) {
      console.log(`   Supporting Evidence:`);
      item.extractedSnippets.forEach((snippet, i) => {
        console.log(`      ${i + 1}. "${snippet}"`);
      });
    } else {
      console.log(`   ⚠️  No supporting evidence found`);
    }
    console.log("");
  });
}
```

#### Example Output Structure

```typescript
// Input: Evaluation result with start_words/end_words
const evaluationResult = {
  claim_analysis: [
    {
      claim: "Solar energy provides clean electricity",
      supporting_context_snippets: [
        {
          start_words: "Solar energy provides clean",
          end_words: "electricity without emissions",
        },
      ],
      reasoning: "Direct support found in context",
      groundedness_score: 95,
    },
  ],
  // ... other fields
};

// Output: Extracted supporting context
const supportingContext = [
  {
    claim: "Solar energy provides clean electricity",
    score: 95,
    extractedSnippets: [
      "Solar energy provides clean electricity without emissions",
    ],
    reasoning: "Direct support found in context",
  },
];
```

#### Use Cases for Programmatic Context Generation

1. **UI Evidence Highlighting**:

   ```typescript
   // Highlight supporting evidence in the UI
   function highlightEvidenceInContext(context: string, snippets: string[]) {
     let highlightedContext = context;
     snippets.forEach((snippet) => {
       const regex = new RegExp(escapeRegex(snippet), "gi");
       highlightedContext = highlightedContext.replace(
         regex,
         `<mark class="evidence-highlight">${snippet}</mark>`
       );
     });
     return highlightedContext;
   }
   ```

2. **Evidence Citation**:

   ```typescript
   // Generate citations for claims
   function generateCitations(
     supportingContext: ReturnType<typeof extractSupportingContext>
   ) {
     return supportingContext
       .filter((item) => item.score >= 70) // Only well-supported claims
       .map((item) => ({
         claim: item.claim,
         evidence: item.extractedSnippets,
         confidence: item.score >= 90 ? "High" : "Medium",
       }));
   }
   ```

3. **Quality Assurance**:

   ```typescript
   // Validate that evidence extraction is working correctly
   function validateEvidenceExtraction(
     evaluation: EvaluationResult,
     originalContext: string
   ): { isValid: boolean; errors: string[] } {
     const errors: string[] = [];

     evaluation.claim_analysis.forEach((claim, index) => {
       if (
         claim.groundedness_score > 0 &&
         claim.supporting_context_snippets.length === 0
       ) {
         errors.push(
           `Claim ${index + 1} has score ${
             claim.groundedness_score
           } but no evidence`
         );
       }

       claim.supporting_context_snippets.forEach((snippet, snippetIndex) => {
         const extracted = extractSnippet(
           originalContext,
           snippet.start_words,
           snippet.end_words
         );
         if (!extracted && claim.groundedness_score > 0) {
           errors.push(
             `Claim ${index + 1}, snippet ${snippetIndex + 1}: ` +
               `Could not extract "${snippet.start_words}...${snippet.end_words}"`
           );
         }
       });
     });

     return {
       isValid: errors.length === 0,
       errors,
     };
   }
   ```

## Implementation Tracking

### Documentation Requirements

As you implement each task, maintain detailed records in:

**`instructs/custom-llm-evaluation-implementation-log.md`**

This file should include:

#### For Each Completed Task:

1. **Task ID and Title**
2. **Implementation Date**
3. **Developer(s) Responsible**
4. **Implementation Approach**
   - Key decisions made
   - Alternative approaches considered
   - Rationale for chosen solution
5. **Technical Details**
   - Code file locations
   - Dependencies added
   - Configuration changes
6. **Testing Performed**
   - Test cases created
   - Validation results
   - Performance measurements
7. **Issues Encountered**
   - Problems faced during implementation
   - Solutions applied
   - Workarounds implemented
8. **Future Considerations**
   - Known limitations
   - Suggested improvements
   - Technical debt introduced

#### Example Entry:

```markdown
## Task 1.1: Set up Evaluation Service Infrastructure

**Date**: 2024-01-15
**Developer**: John Doe

### Implementation Approach

- Chose Express.js over Fastify for consistency with existing codebase
- Used TypeScript for type safety
- Implemented Winston for logging instead of console.log

### Technical Details

- Created `evaluation-service/` directory
- Files: `app.ts`, `config.ts`, `logger.ts`
- Dependencies: express, winston, dotenv, @types/express
- Port: 3001 (configurable via PORT env var)

### Testing

- Health check endpoint responds with 200
- Error handling middleware catches unhandled errors
- Logging outputs to both console and file

### Issues & Solutions

- CORS issue resolved by adding cors middleware
- TypeScript compilation issue fixed by updating tsconfig.json

### Future Considerations

- Add request rate limiting
- Consider moving to microservice architecture
- Add OpenAPI documentation
```

### Status Tracking

Update task completion status in the implementation log:

- ✅ **Completed**: Task fully implemented and tested
- 🔄 **In Progress**: Currently being worked on
- ⏳ **Blocked**: Waiting on dependencies or decisions
- ❌ **Deferred**: Postponed to future iteration

## Integration Points

### Existing Codebase Integration

- **Gemini API**: `app/api/gemini/route.ts` - Add optional evaluation calls
- **Main Page**: `app/page.tsx` - Integrate evaluation panel
- **UI Components**: Extend existing component library
- **Environment**: Update `.env.local` with evaluation service config

### New Components Created

- **Evaluation Service**: Standalone Node.js service for LLM evaluation
- **API Routes**: Next.js routes for evaluation proxy
- **UI Components**: React components for evaluation display
- **Data Schema**: Evaluation results storage structure

## Quality Assurance

### Testing Requirements

- **Unit Tests**: Test individual evaluation functions
- **Integration Tests**: Test full evaluation flow
- **Performance Tests**: Verify <5 second response time
- **Accuracy Tests**: Compare against manual evaluation
- **Hallucination Detection Tests**: Verify programmatic validation catches fake evidence

### Hallucination Detection Testing

**Test Files Available**:

- `instructTests/run-test.ts` - Complete system test including validation
- `instructTests/run-hallucination-test.ts` - Focused hallucination detection tests
- `instructTests/test-fake-evidence.ts` - Comprehensive fake evidence validation

**Run Tests**:

```bash
# Full system test with hallucination detection
npx tsx instructTests/run-test.ts

# Focused hallucination detection test
npx tsx instructTests/run-hallucination-test.ts
```

**Expected Behavior**:

- ✅ Legitimate evidence snippets should be validated successfully
- ❌ Fake/non-existent evidence should be caught and marked with score 0
- 🔍 System should provide clear reasoning for hallucination detection

### Success Criteria

- [x] Single response evaluation with claim breakdown
- [x] Evidence extraction with proper snippet formatting
- [x] 1-100 scoring system implementation (plus score 0 for hallucinations)
- [x] Programmatic validation preventing LLM hallucinations
- [x] Automatic detection and correction of fake evidence
- [ ] Basic UI for evaluation display
- [ ] Integration with existing response generation flow

## Support and Handoff

### For Future Developers

1. **Review the task breakdown** first to understand the complete implementation plan
2. **Follow the structured implementation phases** outlined in the tasks document
3. **Update the implementation log** for each completed task
4. **Test thoroughly** using the provided rubrics
5. **Document any deviations** from the planned approach

### Getting Help

- Review existing LLM integration in `app/api/gemini/route.ts`
- Check knowledge base structure in `mockdata.json`
- Reference UI patterns in existing components
- Follow TypeScript patterns established in the codebase

---

**Next Steps**:

1. Create `instructs/custom-llm-evaluation-implementation-log.md`
2. Begin with Task 1.1 from the task breakdown
3. Update implementation log as you progress

```

```
`````
